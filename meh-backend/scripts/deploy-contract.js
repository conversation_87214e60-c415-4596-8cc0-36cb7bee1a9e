/**
 * MEH Token智能合约部署脚本
 * 
 * 使用方法：
 * node scripts/deploy-contract.js
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */

const Web3 = require('web3');
const fs = require('fs');
const path = require('path');

// Polygon Mumbai测试网配置
const POLYGON_MUMBAI_RPC = 'https://rpc-mumbai.maticvigil.com';
const CHAIN_ID = 80001;

// 部署者私钥（请替换为实际私钥，注意安全）
const DEPLOYER_PRIVATE_KEY = process.env.DEPLOYER_PRIVATE_KEY || 'your-private-key-here';

// 合约编译后的ABI和字节码（需要先编译合约）
const CONTRACT_ABI = [
  // 这里需要填入编译后的ABI
  // 可以使用Remix IDE或Hardhat编译合约获得
];

const CONTRACT_BYTECODE = '0x608060405234801561001057600080fd5b50...'; // 编译后的字节码

/**
 * 部署MEH Token合约
 */
async function deployMehToken() {
  try {
    console.log('🚀 开始部署MEH Business Card Token合约...');
    console.log('📍 网络: Polygon Mumbai测试网');
    console.log('🔗 RPC: ', POLYGON_MUMBAI_RPC);
    
    // 初始化Web3
    const web3 = new Web3(POLYGON_MUMBAI_RPC);
    
    // 创建账户
    const account = web3.eth.accounts.privateKeyToAccount(DEPLOYER_PRIVATE_KEY);
    web3.eth.accounts.wallet.add(account);
    
    console.log('👤 部署者地址:', account.address);
    
    // 检查余额
    const balance = await web3.eth.getBalance(account.address);
    const balanceInMatic = web3.utils.fromWei(balance, 'ether');
    console.log('💰 账户余额:', balanceInMatic, 'MATIC');
    
    if (parseFloat(balanceInMatic) < 0.1) {
      console.error('❌ 余额不足，请确保账户有足够的MATIC用于部署');
      return;
    }
    
    // 获取Gas价格
    const gasPrice = await web3.eth.getGasPrice();
    console.log('⛽ Gas价格:', web3.utils.fromWei(gasPrice, 'gwei'), 'Gwei');
    
    // 创建合约实例
    const contract = new web3.eth.Contract(CONTRACT_ABI);
    
    // 估算Gas
    const deployData = contract.deploy({
      data: CONTRACT_BYTECODE,
      arguments: [] // 构造函数参数
    });
    
    const estimatedGas = await deployData.estimateGas({
      from: account.address
    });
    
    console.log('📊 预估Gas:', estimatedGas);
    
    // 部署合约
    console.log('🔄 正在部署合约...');
    
    const deployedContract = await deployData.send({
      from: account.address,
      gas: Math.floor(estimatedGas * 1.2), // 增加20%的Gas缓冲
      gasPrice: gasPrice
    });
    
    console.log('✅ 合约部署成功！');
    console.log('📄 合约地址:', deployedContract.options.address);
    console.log('🔗 交易哈希:', deployedContract.transactionHash);
    
    // 验证合约
    console.log('🔍 验证合约...');
    const code = await web3.eth.getCode(deployedContract.options.address);
    if (code === '0x') {
      console.error('❌ 合约验证失败：合约地址没有代码');
      return;
    }
    
    // 获取Token信息
    const name = await deployedContract.methods.name().call();
    const symbol = await deployedContract.methods.symbol().call();
    const decimals = await deployedContract.methods.decimals().call();
    const totalSupply = await deployedContract.methods.totalSupply().call();
    
    console.log('📋 Token信息:');
    console.log('   名称:', name);
    console.log('   符号:', symbol);
    console.log('   精度:', decimals);
    console.log('   总供应量:', web3.utils.fromWei(totalSupply, 'ether'));
    
    // 保存部署信息
    const deploymentInfo = {
      contractAddress: deployedContract.options.address,
      transactionHash: deployedContract.transactionHash,
      deployerAddress: account.address,
      network: 'Polygon Mumbai',
      chainId: CHAIN_ID,
      deployedAt: new Date().toISOString(),
      tokenInfo: {
        name,
        symbol,
        decimals: parseInt(decimals),
        totalSupply: totalSupply
      }
    };
    
    // 保存到文件
    const deploymentFile = path.join(__dirname, '../deployment.json');
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    console.log('💾 部署信息已保存到:', deploymentFile);
    
    // 生成配置更新建议
    console.log('\n📝 请更新以下配置:');
    console.log('1. application.yml:');
    console.log(`   polygon.meh-token.contract-address: "${deployedContract.options.address}"`);
    console.log('\n2. 前端配置:');
    console.log(`   mehToken.contractAddress: "${deployedContract.options.address}"`);
    
    console.log('\n🎉 部署完成！');
    
  } catch (error) {
    console.error('❌ 部署失败:', error.message);
    console.error(error);
  }
}

/**
 * 验证合约功能
 */
async function verifyContract(contractAddress) {
  try {
    console.log('🔍 验证合约功能...');
    
    const web3 = new Web3(POLYGON_MUMBAI_RPC);
    const contract = new web3.eth.Contract(CONTRACT_ABI, contractAddress);
    
    // 测试基本功能
    const name = await contract.methods.name().call();
    const symbol = await contract.methods.symbol().call();
    const decimals = await contract.methods.decimals().call();
    const totalSupply = await contract.methods.totalSupply().call();
    
    console.log('✅ 基本信息验证通过');
    console.log('   名称:', name);
    console.log('   符号:', symbol);
    console.log('   精度:', decimals);
    console.log('   总供应量:', web3.utils.fromWei(totalSupply, 'ether'));
    
    // 测试奖励规则
    const dailyLoginReward = await contract.methods.rewardRules('daily_login').call();
    console.log('   每日登录奖励:', web3.utils.fromWei(dailyLoginReward, 'ether'), 'MBC');
    
    console.log('✅ 合约功能验证完成');
    
  } catch (error) {
    console.error('❌ 合约验证失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // 部署合约
    await deployMehToken();
  } else if (args[0] === 'verify' && args[1]) {
    // 验证合约
    await verifyContract(args[1]);
  } else {
    console.log('使用方法:');
    console.log('  部署合约: node scripts/deploy-contract.js');
    console.log('  验证合约: node scripts/deploy-contract.js verify <合约地址>');
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  deployMehToken,
  verifyContract
};
