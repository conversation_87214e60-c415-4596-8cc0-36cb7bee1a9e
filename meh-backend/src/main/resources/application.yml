server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: meh-businesscard

  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************
    username: root
    password:

  # Redis配置
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 10000
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 20MB

  # SpringFox/Swagger配置
  mvc:
    pathmatch:
      matching-strategy: ant-path-matcher

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.meh.businesscard.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  secret: meh-businesscard-secret-key-2025-06-03
  expiration: 86400000  # 24小时
  header: Authorization
  token-prefix: Bearer

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    access-key-id: your-access-key-id
    access-key-secret: your-access-key-secret
    bucket-name: meh-businesscard

# 日志配置
logging:
  level:
    root: info
    com.meh.businesscard: debug
  file:
    name: logs/meh-businesscard.log

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized

# SpringDoc OpenAPI配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method

# 积分规则配置
points:
  rules:
    # 日常活跃
    daily-login: 5
    continuous-login-7days: 10
    continuous-login-30days: 50
    daily-sign: 10

    # 信息完善
    complete-basic-info: 20
    upload-avatar: 10
    complete-introduction: 15
    add-skill-tag: 5
    add-resource-project: 10

    # 社交互动
    like-card: 2
    comment-card: 5
    share-card: 5
    be-liked: 1
    be-commented: 2

    # 人脉拓展
    add-contact: 5
    invite-friend: 50

    # 每日上限
    daily-like-limit: 10
    daily-comment-limit: 5
    daily-share-limit: 3

# 微信小程序配置
wechat:
  appid: your-wechat-appid
  secret: your-wechat-secret
