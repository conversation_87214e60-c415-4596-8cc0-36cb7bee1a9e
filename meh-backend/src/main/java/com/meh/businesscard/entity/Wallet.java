package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包实体类
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("t_wallet")
public class Wallet implements Serializable {

    /**
     * 钱包ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 钱包名称
     */
    private String walletName;

    /**
     * 钱包地址
     */
    private String walletAddress;

    /**
     * 钱包类型：HD、IMPORT、HARDWARE
     */
    private String walletType;

    /**
     * 支持的币种类型：ETH、BTC、USDT等
     */
    private String coinType;

    /**
     * 网络类型：MAINNET、TESTNET
     */
    private String networkType;

    /**
     * 加密的私钥（仅导入钱包存储）
     */
    private String encryptedPrivateKey;

    /**
     * 助记词提示（加密存储）
     */
    private String mnemonicHint;

    /**
     * 钱包余额
     */
    private BigDecimal balance;

    /**
     * 是否为主钱包
     */
    private Boolean isMain;

    /**
     * 钱包状态：ACTIVE、LOCKED、DISABLED
     */
    private String status;

    /**
     * 最后同步时间
     */
    private LocalDateTime lastSyncTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
}
