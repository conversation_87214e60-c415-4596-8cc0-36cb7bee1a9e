package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包交易记录实体类
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("t_wallet_transaction")
public class WalletTransaction implements Serializable {

    /**
     * 交易ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 钱包ID
     */
    private Long walletId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 交易哈希
     */
    private String txHash;

    /**
     * 交易类型：SEND、RECEIVE、APPROVE
     */
    private String txType;

    /**
     * 币种类型
     */
    private String coinType;

    /**
     * 发送方地址
     */
    private String fromAddress;

    /**
     * 接收方地址
     */
    private String toAddress;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * Gas费用
     */
    private BigDecimal gasFee;

    /**
     * Gas价格
     */
    private BigDecimal gasPrice;

    /**
     * Gas限制
     */
    private Long gasLimit;

    /**
     * 确认数
     */
    private Integer confirmations;

    /**
     * 交易状态：PENDING、SUCCESS、FAILED
     */
    private String status;

    /**
     * 区块高度
     */
    private Long blockHeight;

    /**
     * 区块时间
     */
    private LocalDateTime blockTime;

    /**
     * 交易备注
     */
    private String memo;

    /**
     * 关联名片ID（与名片系统集成）
     */
    private Long cardId;

    /**
     * 关联联系人ID
     */
    private Long contactId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
