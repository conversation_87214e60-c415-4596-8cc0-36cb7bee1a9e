package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 钱包地址簿实体类
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("t_wallet_address_book")
public class WalletAddressBook implements Serializable {

    /**
     * 地址簿ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 联系人名称
     */
    private String contactName;

    /**
     * 钱包地址
     */
    private String walletAddress;

    /**
     * 币种类型
     */
    private String coinType;

    /**
     * 地址标签
     */
    private String addressLabel;

    /**
     * 地址分类：PERSONAL、BUSINESS、EXCHANGE
     */
    private String addressCategory;

    /**
     * 地址备注
     */
    private String remark;

    /**
     * 关联的名片ID（与名片系统集成）
     */
    private Long cardId;

    /**
     * 关联的联系人ID
     */
    private Long contactId;

    /**
     * 是否常用地址
     */
    private Boolean isFrequent;

    /**
     * 风险等级：LOW、MEDIUM、HIGH
     */
    private String riskLevel;

    /**
     * 使用次数
     */
    private Integer useCount;

    /**
     * 最后使用时间
     */
    private LocalDateTime lastUsedTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    private Boolean deleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
}
