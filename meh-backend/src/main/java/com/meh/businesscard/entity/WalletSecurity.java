package com.meh.businesscard.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包安全设置实体类
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@TableName("t_wallet_security")
public class WalletSecurity implements Serializable {

    /**
     * 安全设置ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 钱包ID
     */
    private Long walletId;

    /**
     * 交易密码（加密存储）
     */
    private String transactionPassword;

    /**
     * 是否启用生物识别
     */
    private Boolean biometricEnabled;

    /**
     * 生物识别类型：FINGERPRINT、FACE、VOICE
     */
    private String biometricType;

    /**
     * 是否启用多重签名
     */
    private Boolean multiSigEnabled;

    /**
     * 多重签名阈值
     */
    private Integer multiSigThreshold;

    /**
     * 多重签名参与者数量
     */
    private Integer multiSigParticipants;

    /**
     * 设备绑定状态
     */
    private Boolean deviceBindingEnabled;

    /**
     * 绑定设备标识
     */
    private String deviceFingerprint;

    /**
     * 日交易限额
     */
    private BigDecimal dailyLimit;

    /**
     * 月交易限额
     */
    private BigDecimal monthlyLimit;

    /**
     * 大额交易阈值
     */
    private BigDecimal largeAmountThreshold;

    /**
     * 是否启用冷却期
     */
    private Boolean coolingPeriodEnabled;

    /**
     * 冷却期时长（小时）
     */
    private Integer coolingPeriodHours;

    /**
     * 最后验证时间
     */
    private LocalDateTime lastVerifyTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
