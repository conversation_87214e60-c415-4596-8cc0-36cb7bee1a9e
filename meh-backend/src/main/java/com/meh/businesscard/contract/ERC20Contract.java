package com.meh.businesscard.contract;

import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.FunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.abi.datatypes.generated.Uint8;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.EthCall;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * ERC-20 Token合约交互类
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public class ERC20Contract {

    private final Web3j web3j;
    private final String contractAddress;

    public ERC20Contract(Web3j web3j, String contractAddress) {
        this.web3j = web3j;
        this.contractAddress = contractAddress;
    }

    /**
     * 获取Token名称
     */
    public CompletableFuture<String> name() {
        Function function = new Function(
                "name",
                Collections.emptyList(),
                Arrays.asList(new TypeReference<Utf8String>() {})
        );

        return executeCallSingleValueReturn(function, String.class);
    }

    /**
     * 获取Token符号
     */
    public CompletableFuture<String> symbol() {
        Function function = new Function(
                "symbol",
                Collections.emptyList(),
                Arrays.asList(new TypeReference<Utf8String>() {})
        );

        return executeCallSingleValueReturn(function, String.class);
    }

    /**
     * 获取Token精度
     */
    public CompletableFuture<BigInteger> decimals() {
        Function function = new Function(
                "decimals",
                Collections.emptyList(),
                Arrays.asList(new TypeReference<Uint8>() {})
        );

        return executeCallSingleValueReturn(function, BigInteger.class);
    }

    /**
     * 获取总供应量
     */
    public CompletableFuture<BigInteger> totalSupply() {
        Function function = new Function(
                "totalSupply",
                Collections.emptyList(),
                Arrays.asList(new TypeReference<Uint256>() {})
        );

        return executeCallSingleValueReturn(function, BigInteger.class);
    }

    /**
     * 获取指定地址的余额
     * 
     * @param owner 地址
     * @return 余额
     */
    public CompletableFuture<BigInteger> balanceOf(String owner) {
        Function function = new Function(
                "balanceOf",
                Arrays.asList(new Address(owner)),
                Arrays.asList(new TypeReference<Uint256>() {})
        );

        return executeCallSingleValueReturn(function, BigInteger.class);
    }

    /**
     * 获取授权额度
     * 
     * @param owner 所有者地址
     * @param spender 被授权地址
     * @return 授权额度
     */
    public CompletableFuture<BigInteger> allowance(String owner, String spender) {
        Function function = new Function(
                "allowance",
                Arrays.asList(new Address(owner), new Address(spender)),
                Arrays.asList(new TypeReference<Uint256>() {})
        );

        return executeCallSingleValueReturn(function, BigInteger.class);
    }

    /**
     * 创建转账交易数据
     * 
     * @param to 接收地址
     * @param value 转账金额
     * @return 交易数据
     */
    public String createTransferData(String to, BigInteger value) {
        Function function = new Function(
                "transfer",
                Arrays.asList(new Address(to), new Uint256(value)),
                Collections.emptyList()
        );

        return FunctionEncoder.encode(function);
    }

    /**
     * 创建授权交易数据
     * 
     * @param spender 被授权地址
     * @param value 授权金额
     * @return 交易数据
     */
    public String createApproveData(String spender, BigInteger value) {
        Function function = new Function(
                "approve",
                Arrays.asList(new Address(spender), new Uint256(value)),
                Collections.emptyList()
        );

        return FunctionEncoder.encode(function);
    }

    /**
     * 创建从指定地址转账的交易数据
     * 
     * @param from 发送地址
     * @param to 接收地址
     * @param value 转账金额
     * @return 交易数据
     */
    public String createTransferFromData(String from, String to, BigInteger value) {
        Function function = new Function(
                "transferFrom",
                Arrays.asList(new Address(from), new Address(to), new Uint256(value)),
                Collections.emptyList()
        );

        return FunctionEncoder.encode(function);
    }

    /**
     * 执行合约调用并返回单个值
     */
    @SuppressWarnings("unchecked")
    private <T> CompletableFuture<T> executeCallSingleValueReturn(Function function, Class<T> returnType) {
        String encodedFunction = FunctionEncoder.encode(function);
        
        return web3j.ethCall(
                Transaction.createEthCallTransaction(null, contractAddress, encodedFunction),
                DefaultBlockParameterName.LATEST
        ).sendAsync().thenApply(ethCall -> {
            String value = ethCall.getValue();
            if (value == null || value.equals("0x")) {
                return null;
            }
            
            List<Type> results = FunctionReturnDecoder.decode(value, function.getOutputParameters());
            if (results.isEmpty()) {
                return null;
            }
            
            Type result = results.get(0);
            Object resultValue = result.getValue();
            
            if (returnType == String.class) {
                return (T) resultValue;
            } else if (returnType == BigInteger.class) {
                return (T) resultValue;
            } else {
                return (T) resultValue;
            }
        });
    }

    /**
     * 估算Gas限制
     * 
     * @param from 发送地址
     * @param data 交易数据
     * @return Gas限制
     */
    public CompletableFuture<BigInteger> estimateGas(String from, String data) {
        Transaction transaction = Transaction.createEthCallTransaction(from, contractAddress, data);
        
        return web3j.ethEstimateGas(transaction).sendAsync().thenApply(ethEstimateGas -> {
            if (ethEstimateGas.hasError()) {
                throw new RuntimeException("Gas estimation failed: " + ethEstimateGas.getError().getMessage());
            }
            return ethEstimateGas.getAmountUsed();
        });
    }

    /**
     * 检查合约是否存在
     */
    public CompletableFuture<Boolean> isContractValid() {
        return web3j.ethGetCode(contractAddress, DefaultBlockParameterName.LATEST)
                .sendAsync()
                .thenApply(ethGetCode -> {
                    String code = ethGetCode.getCode();
                    return code != null && !code.equals("0x") && !code.equals("0x0");
                });
    }

    /**
     * 获取合约地址
     */
    public String getContractAddress() {
        return contractAddress;
    }
}
