package com.meh.businesscard.controller;

import com.meh.businesscard.common.annotation.OperationLog;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.entity.UserPoints;
import com.meh.businesscard.entity.UserPointsLog;
import com.meh.businesscard.service.UserService;
import com.meh.businesscard.service.UserPointsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户控制器
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Tag(name = "用户管理")
@RestController
@RequestMapping("/users")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private UserPointsService userPointsService;

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
        @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @GetMapping("/current")
    @OperationLog(module = "用户管理", operationType = "查询", description = "获取当前用户信息")
    public Result<User> getCurrentUser() {
        User currentUser = userService.getCurrentUser();
        return Result.success(currentUser);
    }

    /**
     * 获取用户信息（兼容前端调用）
     *
     * @return 用户信息
     */
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息（兼容接口）")
    @GetMapping("/info")
    @OperationLog(module = "用户管理", operationType = "查询", description = "获取用户信息")
    public Result<User> getUserInfo() {
        User currentUser = userService.getCurrentUser();
        return Result.success(currentUser);
    }

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 更新结果
     */
        @Operation(summary = "更新用户信息", description = "更新当前用户的基本信息")
    @PutMapping("/current")
    @OperationLog(module = "用户管理", operationType = "修改", description = "更新用户信息")
    public Result<?> updateCurrentUser(@RequestBody User user) {
        User currentUser = userService.getCurrentUser();
        user.setId(currentUser.getId());
        
        // 只允许更新部分字段
        User updateUser = new User();
        updateUser.setId(currentUser.getId());
        updateUser.setNickname(user.getNickname());
        updateUser.setAvatar(user.getAvatar());
        updateUser.setGender(user.getGender());
        updateUser.setRealName(user.getRealName());
        updateUser.setEmail(user.getEmail());
        
        boolean success = userService.updateById(updateUser);
        if (success) {
            return Result.success();
        } else {
            return Result.failed("更新失败");
        }
    }

    /**
     * 上传头像
     *
     * @param file 头像文件
     * @return 上传结果
     */
        @Operation(summary = "上传头像", description = "上传用户头像图片")
    @PostMapping("/avatar")
    @OperationLog(module = "用户管理", operationType = "上传", description = "上传用户头像")
    public Result<String> uploadAvatar(@RequestParam("file") MultipartFile file) {
        try {
            // 校验文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.failed("只支持图片文件");
            }

            // 校验文件大小（2MB）
            if (file.getSize() > 2 * 1024 * 1024) {
                return Result.failed("头像大小不能超过2MB");
            }

            // 生成文件名
            String fileName = "avatar_" + System.currentTimeMillis() + ".jpg";
            String avatarUrl = "https://example.com/uploads/avatars/" + fileName;

            // 更新用户头像
            User currentUser = userService.getCurrentUser();
            currentUser.setAvatar(avatarUrl);
            userService.updateById(currentUser);

            return Result.success(avatarUrl);
        } catch (Exception e) {
            return Result.failed("上传失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户积分信息
     *
     * @return 积分信息
     */
        @Operation(summary = "获取用户积分信息", description = "获取当前用户的积分详情")
    @GetMapping("/points")
    @OperationLog(module = "用户管理", operationType = "查询", description = "获取用户积分信息")
    public Result<UserPoints> getUserPoints() {
        User currentUser = userService.getCurrentUser();
        UserPoints userPoints = userPointsService.getUserPoints(currentUser.getId());
        return Result.success(userPoints);
    }

    /**
     * 获取积分变动记录
     *
     * @param page 页码
     * @param size 每页大小
     * @return 积分变动记录
     */
        @Operation(summary = "获取积分变动记录", description = "分页获取用户积分变动历史记录")
    ////@ApiImplicitParams({
            //@ApiImplicitParam(name = "page", value = "页码", defaultValue = "1", paramType = "query", dataTypeClass = Integer.class),
            //@ApiImplicitParam(name = "size", value = "每页大小", defaultValue = "20", paramType = "query", dataTypeClass = Integer.class)
    //})
    @GetMapping("/points/logs")
    @OperationLog(module = "用户管理", operationType = "查询", description = "获取积分变动记录")
    public Result<List<UserPointsLog>> getPointsLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        User currentUser = userService.getCurrentUser();
        List<UserPointsLog> logs = userPointsService.getPointsLogs(currentUser.getId(), page, size);
        return Result.success(logs);
    }

    /**
     * 每日签到
     *
     * @return 签到结果
     */
        @Operation(summary = "每日签到", description = "用户每日签到获取积分")
    @PostMapping("/checkin")
    @OperationLog(module = "用户管理", operationType = "签到", description = "用户每日签到")
    public Result<?> dailyCheckin() {
        User currentUser = userService.getCurrentUser();
        return userPointsService.dailyCheckin(currentUser.getId());
    }

    /**
     * 获取用户统计信息
     *
     * @return 统计信息
     */
        @Operation(summary = "获取用户统计信息", description = "获取用户的各项统计数据")
    @GetMapping("/stats")
    @OperationLog(module = "用户管理", operationType = "查询", description = "获取用户统计信息")
    public Result<?> getUserStats() {
        User currentUser = userService.getCurrentUser();

        // 构建用户统计信息
        Map<String, Object> stats = new HashMap<>();
        stats.put("userId", currentUser.getId());
        stats.put("cardCount", 0); // TODO: 从CardService获取用户名片数量
        stats.put("contactCount", 0); // TODO: 从ContactService获取联系人数量
        stats.put("visitorCount", 0); // TODO: 从访客记录获取访客数量
        stats.put("totalPoints", 0); // TODO: 从UserPointsService获取总积分
        stats.put("joinDate", currentUser.getCreateTime());

        return Result.success(stats);
    }
}
