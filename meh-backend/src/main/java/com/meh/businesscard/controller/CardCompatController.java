package com.meh.businesscard.controller;

import com.meh.businesscard.common.annotation.OperationLog;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.service.CardService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 名片兼容控制器（兼容前端调用）
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Tag(name = "名片兼容接口")
@RestController
@RequestMapping("/card")
public class CardCompatController {

    @Autowired
    private CardService cardService;

    /**
     * 获取名片列表（兼容前端调用）
     *
     * @return 名片列表
     */
    @Operation(summary = "获取名片列表", description = "获取当前用户的所有名片（兼容接口）")
    @GetMapping("/list")
    @OperationLog(module = "名片管理", operationType = "查询", description = "获取名片列表")
    public Result<?> getCardList() {
        return cardService.getMyCardList();
    }

    /**
     * 获取推荐名片列表（兼容前端调用）
     *
     * @return 推荐名片列表
     */
    @Operation(summary = "获取推荐名片", description = "获取系统推荐的名片列表（兼容接口）")
    @GetMapping("/recommend")
    @OperationLog(module = "名片管理", operationType = "查询", description = "获取推荐名片列表")
    public Result<?> getRecommendCards() {
        return cardService.getRecommendCards();
    }
}
