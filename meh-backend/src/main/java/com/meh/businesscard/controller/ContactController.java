package com.meh.businesscard.controller;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.ContactCreateDTO;
import com.meh.businesscard.dto.ContactUpdateDTO;
import com.meh.businesscard.entity.Contact;
import com.meh.businesscard.service.ContactService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通讯录控制器
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Tag(name = "通讯录管理")
@RestController
@RequestMapping("/contacts")
public class ContactController {

    @Autowired
    private ContactService contactService;

    /**
     * 添加联系人
     *
     * @param contactCreateDTO 联系人创建参数
     * @return 创建结果
     */
    @Operation(summary = "添加联系人", description = "添加新的联系人到通讯录")
    @PostMapping
    public Result<?> createContact(@RequestBody @Validated ContactCreateDTO contactCreateDTO) {
        return contactService.createContact(contactCreateDTO);
    }

    /**
     * 更新联系人
     *
     * @param id 联系人ID
     * @param contactUpdateDTO 联系人更新参数
     * @return 更新结果
     */
    @Operation(summary = "更新联系人", description = "更新指定联系人的信息")
    @PutMapping("/{id}")
    public Result<?> updateContact(@PathVariable Long id, @RequestBody @Validated ContactUpdateDTO contactUpdateDTO) {
        contactUpdateDTO.setId(id);
        return contactService.updateContact(contactUpdateDTO);
    }

    /**
     * 删除联系人
     *
     * @param id 联系人ID
     * @return 删除结果
     */
    @Operation(summary = "删除联系人", description = "从通讯录中删除指定联系人")
    @DeleteMapping("/{id}")
    public Result<?> deleteContact(@PathVariable Long id) {
        return contactService.deleteContact(id);
    }

    /**
     * 获取联系人详情
     *
     * @param id 联系人ID
     * @return 联系人详情
     */
    @Operation(summary = "获取联系人详情", description = "获取指定联系人的详细信息")
    @GetMapping("/{id}")
    public Result<Contact> getContactDetail(@PathVariable Long id) {
        return contactService.getContactDetail(id);
    }

    /**
     * 获取我的联系人列表
     *
     * @param keyword 搜索关键词
     * @param groupId 分组ID
     * @return 联系人列表
     */
    @Operation(summary = "获取我的联系人列表", description = "获取当前用户的联系人列表，支持关键词搜索和分组筛选")
    @GetMapping
    public Result<List<Contact>> getMyContactList(@RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long groupId) {
        return contactService.getMyContactList(keyword, groupId);
    }

    /**
     * 设置星标联系人
     *
     * @param id 联系人ID
     * @param isStarred 是否星标：0-否，1-是
     * @return 设置结果
     */
    @Operation(summary = "设置星标联系人", description = "将指定联系人设置为星标或取消星标")
    @PutMapping("/{id}/star")
    public Result<?> starContact(@PathVariable Long id, @RequestParam Integer isStarred) {
        return contactService.starContact(id, isStarred);
    }

    /**
     * 获取联系人分组列表
     *
     * @return 分组列表
     */
    @Operation(summary = "获取联系人分组列表", description = "获取当前用户的所有联系人分组")
    @GetMapping("/groups")
    public Result<?> getContactGroupList() {
        return contactService.getContactGroupList();
    }

    /**
     * 创建联系人分组
     *
     * @param name 分组名称
     * @return 创建结果
     */
    @Operation(summary = "创建联系人分组", description = "创建新的联系人分组")
    @PostMapping("/groups")
    public Result<?> createContactGroup(@RequestParam String name) {
        return contactService.createContactGroup(name);
    }

    /**
     * 更新联系人分组
     *
     * @param id 分组ID
     * @param name 分组名称
     * @return 更新结果
     */
    @Operation(summary = "更新联系人分组", description = "更新指定联系人分组的名称")
    @PutMapping("/groups/{id}")
    public Result<?> updateContactGroup(@PathVariable Long id, @RequestParam String name) {
        return contactService.updateContactGroup(id, name);
    }

    /**
     * 删除联系人分组
     *
     * @param id 分组ID
     * @return 删除结果
     */
    @Operation(summary = "删除联系人分组", description = "删除指定的联系人分组")
    @DeleteMapping("/groups/{id}")
    public Result<?> deleteContactGroup(@PathVariable Long id) {
        return contactService.deleteContactGroup(id);
    }
}
