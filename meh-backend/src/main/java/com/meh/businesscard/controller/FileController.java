package com.meh.businesscard.controller;

import com.meh.businesscard.common.annotation.OperationLog;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.service.FileService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传控制器
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Tag(name = "文件管理")
@RestController
@RequestMapping("/file")
public class FileController {

    @Autowired
    private FileService fileService;

    /**
     * 上传图片
     *
     * @param file 图片文件
     * @return 上传结果
     */
    @Operation(summary = "上传图片", description = "上传图片文件，支持jpg、png、gif格式")
    //@ApiImplicitParam(name = "file", value = "图片文件", required = true, dataType = "file", paramType = "form")
    @PostMapping("/upload/image")
    @OperationLog(module = "文件管理", operationType = "上传", description = "上传图片")
    public Result<String> uploadImage(@RequestParam("file") MultipartFile file) {
        try {
            // 校验文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.failed("只支持图片文件");
            }

            // 校验文件大小（5MB）
            if (file.getSize() > 5 * 1024 * 1024) {
                return Result.failed("图片大小不能超过5MB");
            }

            String imageUrl = fileService.uploadImage(file);
            return Result.success(imageUrl);
        } catch (Exception e) {
            return Result.failed("上传失败：" + e.getMessage());
        }
    }

    /**
     * 上传头像
     *
     * @param file 头像文件
     * @return 上传结果
     */
        @Operation(summary = "上传头像", description = "上传用户头像，会自动裁剪为正方形")
    //@ApiImplicitParam(name = "file", value = "头像文件", required = true, dataType = "file", paramType = "form")
    @PostMapping("/upload/avatar")
    @OperationLog(module = "文件管理", operationType = "上传", description = "上传头像")
    public Result<String> uploadAvatar(@RequestParam("file") MultipartFile file) {
        try {
            // 校验文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.failed("只支持图片文件");
            }

            // 校验文件大小（2MB）
            if (file.getSize() > 2 * 1024 * 1024) {
                return Result.failed("头像大小不能超过2MB");
            }

            String avatarUrl = fileService.uploadAvatar(file);
            return Result.success(avatarUrl);
        } catch (Exception e) {
            return Result.failed("上传失败：" + e.getMessage());
        }
    }

    /**
     * 上传名片背景
     *
     * @param file 背景图片文件
     * @return 上传结果
     */
        @Operation(summary = "上传名片背景", description = "上传名片背景图片")
    //@ApiImplicitParam(name = "file", value = "背景图片文件", required = true, dataType = "file", paramType = "form")
    @PostMapping("/upload/card-bg")
    @OperationLog(module = "文件管理", operationType = "上传", description = "上传名片背景")
    public Result<String> uploadCardBackground(@RequestParam("file") MultipartFile file) {
        try {
            // 校验文件类型
            String contentType = file.getContentType();
            if (contentType == null || !contentType.startsWith("image/")) {
                return Result.failed("只支持图片文件");
            }

            // 校验文件大小（3MB）
            if (file.getSize() > 3 * 1024 * 1024) {
                return Result.failed("背景图片大小不能超过3MB");
            }

            String backgroundUrl = fileService.uploadCardBackground(file);
            return Result.success(backgroundUrl);
        } catch (Exception e) {
            return Result.failed("上传失败：" + e.getMessage());
        }
    }

    /**
     * 批量上传图片
     *
     * @param files 图片文件数组
     * @return 上传结果
     */
        @Operation(summary = "批量上传图片", description = "批量上传多张图片")
    @PostMapping("/upload/images")
    @OperationLog(module = "文件管理", operationType = "上传", description = "批量上传图片")
    public Result<?> uploadImages(@RequestParam("files") MultipartFile[] files) {
        try {
            if (files.length > 9) {
                return Result.failed("最多只能上传9张图片");
            }

            Map<String, Object> result = new HashMap<>();
            String[] imageUrls = new String[files.length];
            
            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];
                
                // 校验文件类型
                String contentType = file.getContentType();
                if (contentType == null || !contentType.startsWith("image/")) {
                    return Result.failed("第" + (i + 1) + "个文件不是图片格式");
                }

                // 校验文件大小（5MB）
                if (file.getSize() > 5 * 1024 * 1024) {
                    return Result.failed("第" + (i + 1) + "个图片大小不能超过5MB");
                }

                imageUrls[i] = fileService.uploadImage(file);
            }
            
            result.put("imageUrls", imageUrls);
            result.put("count", files.length);
            
            return Result.success(result);
        } catch (Exception e) {
            return Result.failed("上传失败：" + e.getMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param fileUrl 文件URL
     * @return 删除结果
     */
        @Operation(summary = "删除文件", description = "删除指定的文件")
    //@ApiImplicitParam(name = "fileUrl", value = "文件URL", required = true, paramType = "query", dataTypeClass = String.class)
    @DeleteMapping("/delete")
    @OperationLog(module = "文件管理", operationType = "删除", description = "删除文件")
    public Result<?> deleteFile(@RequestParam String fileUrl) {
        try {
            boolean success = fileService.deleteFile(fileUrl);
            if (success) {
                return Result.success();
            } else {
                return Result.failed("删除失败");
            }
        } catch (Exception e) {
            return Result.failed("删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取文件信息
     *
     * @param fileUrl 文件URL
     * @return 文件信息
     */
        @Operation(summary = "获取文件信息", description = "获取文件的详细信息")
    //@ApiImplicitParam(name = "fileUrl", value = "文件URL", required = true, paramType = "query", dataTypeClass = String.class)
    @GetMapping("/info")
    @OperationLog(module = "文件管理", operationType = "查询", description = "获取文件信息")
    public Result<?> getFileInfo(@RequestParam String fileUrl) {
        try {
            Map<String, Object> fileInfo = fileService.getFileInfo(fileUrl);
            return Result.success(fileInfo);
        } catch (Exception e) {
            return Result.failed("获取文件信息失败：" + e.getMessage());
        }
    }
}
