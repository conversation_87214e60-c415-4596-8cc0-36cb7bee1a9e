package com.meh.businesscard.controller;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.*;
import com.meh.businesscard.service.WalletAddressBookService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 钱包地址簿控制器
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Tag(name = "钱包地址簿")
@RestController
@RequestMapping("/wallet-address-book")
@RequiredArgsConstructor
public class WalletAddressBookController {

    private final WalletAddressBookService addressBookService;

    /**
     * 添加联系人地址
     */
    @Operation(summary = "添加联系人", description = "添加常用收款地址到地址簿")
    @PostMapping
    public Result<?> addContact(@RequestBody @Validated AddressBookCreateDTO dto) {
        return addressBookService.addContact(dto);
    }

    /**
     * 获取地址簿列表
     */
    @Operation(summary = "获取地址簿", description = "获取用户的钱包地址簿")
    @GetMapping
    public Result<?> getAddressBook(
            @RequestParam(required = false) String coinType,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize) {
        return addressBookService.getAddressBook(coinType, category, keyword, pageNum, pageSize);
    }

    /**
     * 更新联系人信息
     */
    @Operation(summary = "更新联系人", description = "更新地址簿中的联系人信息")
    @PutMapping("/{contactId}")
    public Result<?> updateContact(@PathVariable Long contactId, @RequestBody AddressBookUpdateDTO dto) {
        return addressBookService.updateContact(contactId, dto);
    }

    /**
     * 删除联系人
     */
    @Operation(summary = "删除联系人", description = "从地址簿中删除联系人")
    @DeleteMapping("/{contactId}")
    public Result<?> deleteContact(@PathVariable Long contactId) {
        return addressBookService.deleteContact(contactId);
    }

    /**
     * 批量删除联系人
     */
    @Operation(summary = "批量删除", description = "批量删除多个联系人")
    @DeleteMapping("/batch")
    public Result<?> batchDeleteContacts(@RequestBody BatchDeleteDTO dto) {
        return addressBookService.batchDeleteContacts(dto);
    }

    /**
     * 关联名片
     */
    @Operation(summary = "关联名片", description = "将地址簿联系人关联到电子名片")
    @PostMapping("/{contactId}/bind-card")
    public Result<?> bindToCard(@PathVariable Long contactId, @RequestParam Long cardId) {
        return addressBookService.bindToCard(contactId, cardId);
    }

    /**
     * 从名片导入地址
     */
    @Operation(summary = "从名片导入", description = "从电子名片联系人导入钱包地址")
    @PostMapping("/import-from-cards")
    public Result<?> importFromCards(@RequestBody CardImportDTO dto) {
        return addressBookService.importFromCards(dto);
    }

    /**
     * 设置常用地址
     */
    @Operation(summary = "设置常用地址", description = "将地址设置为常用地址")
    @PostMapping("/{contactId}/set-frequent")
    public Result<?> setFrequentAddress(@PathVariable Long contactId, @RequestParam Boolean frequent) {
        return addressBookService.setFrequentAddress(contactId, frequent);
    }

    /**
     * 获取最近联系人
     */
    @Operation(summary = "获取最近联系人", description = "获取最近使用的联系人地址")
    @GetMapping("/recent")
    public Result<?> getRecentContacts(@RequestParam(required = false) String coinType) {
        return addressBookService.getRecentContacts(coinType);
    }

    /**
     * 获取常用地址
     */
    @Operation(summary = "获取常用地址", description = "获取常用的收款地址列表")
    @GetMapping("/frequent")
    public Result<?> getFrequentAddresses(@RequestParam(required = false) String coinType) {
        return addressBookService.getFrequentAddresses(coinType);
    }

    /**
     * 验证地址有效性
     */
    @Operation(summary = "验证地址", description = "验证钱包地址的有效性和风险等级")
    @PostMapping("/validate-address")
    public Result<?> validateAddress(@RequestParam String address, @RequestParam String coinType) {
        return addressBookService.validateAddress(address, coinType);
    }

    /**
     * 导出地址簿
     */
    @Operation(summary = "导出地址簿", description = "导出地址簿为CSV文件")
    @GetMapping("/export")
    public Result<?> exportAddressBook() {
        return addressBookService.exportAddressBook();
    }

    /**
     * 导入地址簿
     */
    @Operation(summary = "导入地址簿", description = "从CSV文件批量导入地址")
    @PostMapping("/import")
    public Result<?> importAddressBook(@RequestParam("file") MultipartFile file) {
        return addressBookService.importAddressBook(file);
    }

    /**
     * 获取地址标签
     */
    @Operation(summary = "获取地址标签", description = "获取可用的地址标签列表")
    @GetMapping("/labels")
    public Result<?> getAddressLabels() {
        return addressBookService.getAddressLabels();
    }

    /**
     * 创建地址标签
     */
    @Operation(summary = "创建地址标签", description = "创建自定义地址标签")
    @PostMapping("/labels")
    public Result<?> createAddressLabel(@RequestBody LabelCreateDTO dto) {
        return addressBookService.createAddressLabel(dto);
    }
}
