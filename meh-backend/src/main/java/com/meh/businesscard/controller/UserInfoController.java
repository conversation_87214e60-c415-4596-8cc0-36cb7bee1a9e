package com.meh.businesscard.controller;

import com.meh.businesscard.common.annotation.OperationLog;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.service.UserService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户信息控制器（兼容前端调用）
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Tag(name = "用户信息")
@RestController
@RequestMapping("/user")
public class UserInfoController {

    @Autowired
    private UserService userService;

    /**
     * 获取用户信息（兼容前端调用）
     *
     * @return 用户信息
     */
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息（兼容接口）")
    @GetMapping("/info")
    @OperationLog(module = "用户管理", operationType = "查询", description = "获取用户信息")
    public Result<User> getUserInfo() {
        User currentUser = userService.getCurrentUser();
        return Result.success(currentUser);
    }
}
