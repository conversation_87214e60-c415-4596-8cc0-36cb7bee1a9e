package com.meh.businesscard.controller;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.*;
import com.meh.businesscard.service.WalletSecurityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 钱包安全控制器
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Tag(name = "钱包安全")
@RestController
@RequestMapping("/wallet-security")
@RequiredArgsConstructor
public class WalletSecurityController {

    private final WalletSecurityService securityService;

    /**
     * 设置交易密码
     */
    @Operation(summary = "设置交易密码", description = "设置或修改钱包交易密码")
    @PostMapping("/transaction-password")
    public Result<?> setTransactionPassword(@RequestBody @Validated TransactionPasswordDTO dto) {
        return securityService.setTransactionPassword(dto);
    }

    /**
     * 验证交易密码
     */
    @Operation(summary = "验证交易密码", description = "验证交易密码是否正确")
    @PostMapping("/verify-password")
    public Result<?> verifyTransactionPassword(@RequestParam String password) {
        return securityService.verifyTransactionPassword(password);
    }

    /**
     * 启用生物识别
     */
    @Operation(summary = "启用生物识别", description = "启用指纹或面部识别验证")
    @PostMapping("/enable-biometric")
    public Result<?> enableBiometric(@RequestBody @Validated BiometricSetupDTO dto) {
        return securityService.enableBiometric(dto);
    }

    /**
     * 禁用生物识别
     */
    @Operation(summary = "禁用生物识别", description = "禁用生物识别验证")
    @PostMapping("/disable-biometric")
    public Result<?> disableBiometric(@RequestParam String password) {
        return securityService.disableBiometric(password);
    }

    /**
     * 验证生物识别
     */
    @Operation(summary = "验证生物识别", description = "验证生物识别信息")
    @PostMapping("/verify-biometric")
    public Result<?> verifyBiometric(@RequestBody BiometricVerifyDTO dto) {
        return securityService.verifyBiometric(dto);
    }

    /**
     * 设置多重签名
     */
    @Operation(summary = "设置多重签名", description = "配置多重签名钱包")
    @PostMapping("/multi-signature")
    public Result<?> setupMultiSignature(@RequestBody @Validated MultiSigSetupDTO dto) {
        return securityService.setupMultiSignature(dto);
    }

    /**
     * 获取多重签名配置
     */
    @Operation(summary = "获取多签配置", description = "获取钱包的多重签名配置")
    @GetMapping("/{walletId}/multi-signature")
    public Result<?> getMultiSignatureConfig(@PathVariable Long walletId) {
        return securityService.getMultiSignatureConfig(walletId);
    }

    /**
     * 设置交易限额
     */
    @Operation(summary = "设置交易限额", description = "设置日/月交易限额")
    @PostMapping("/transaction-limits")
    public Result<?> setTransactionLimits(@RequestBody @Validated TransactionLimitsDTO dto) {
        return securityService.setTransactionLimits(dto);
    }

    /**
     * 获取交易限额
     */
    @Operation(summary = "获取交易限额", description = "获取当前的交易限额设置")
    @GetMapping("/{walletId}/transaction-limits")
    public Result<?> getTransactionLimits(@PathVariable Long walletId) {
        return securityService.getTransactionLimits(walletId);
    }

    /**
     * 绑定设备
     */
    @Operation(summary = "绑定设备", description = "绑定当前设备到钱包")
    @PostMapping("/bind-device")
    public Result<?> bindDevice(@RequestBody @Validated DeviceBindingDTO dto) {
        return securityService.bindDevice(dto);
    }

    /**
     * 解绑设备
     */
    @Operation(summary = "解绑设备", description = "解绑指定设备")
    @DeleteMapping("/device/{deviceId}")
    public Result<?> unbindDevice(@PathVariable String deviceId, @RequestParam String password) {
        return securityService.unbindDevice(deviceId, password);
    }

    /**
     * 获取绑定设备列表
     */
    @Operation(summary = "获取绑定设备", description = "获取钱包绑定的设备列表")
    @GetMapping("/bound-devices")
    public Result<?> getBoundDevices() {
        return securityService.getBoundDevices();
    }

    /**
     * 设置冷却期
     */
    @Operation(summary = "设置冷却期", description = "设置大额交易冷却期")
    @PostMapping("/cooling-period")
    public Result<?> setCoolingPeriod(@RequestBody @Validated CoolingPeriodDTO dto) {
        return securityService.setCoolingPeriod(dto);
    }

    /**
     * 获取安全设置
     */
    @Operation(summary = "获取安全设置", description = "获取钱包的所有安全设置")
    @GetMapping("/{walletId}/settings")
    public Result<?> getSecuritySettings(@PathVariable Long walletId) {
        return securityService.getSecuritySettings(walletId);
    }

    /**
     * 安全检查
     */
    @Operation(summary = "安全检查", description = "执行钱包安全检查")
    @PostMapping("/{walletId}/security-check")
    public Result<?> performSecurityCheck(@PathVariable Long walletId) {
        return securityService.performSecurityCheck(walletId);
    }

    /**
     * 获取安全日志
     */
    @Operation(summary = "获取安全日志", description = "获取钱包安全操作日志")
    @GetMapping("/{walletId}/security-logs")
    public Result<?> getSecurityLogs(
            @PathVariable Long walletId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "20") Integer pageSize) {
        return securityService.getSecurityLogs(walletId, pageNum, pageSize);
    }

    /**
     * 紧急冻结钱包
     */
    @Operation(summary = "紧急冻结", description = "紧急冻结钱包防止资产损失")
    @PostMapping("/{walletId}/emergency-freeze")
    public Result<?> emergencyFreeze(@PathVariable Long walletId, @RequestParam String reason) {
        return securityService.emergencyFreeze(walletId, reason);
    }

    /**
     * 解冻钱包
     */
    @Operation(summary = "解冻钱包", description = "解除钱包冻结状态")
    @PostMapping("/{walletId}/unfreeze")
    public Result<?> unfreezeWallet(@PathVariable Long walletId, @RequestParam String password) {
        return securityService.unfreezeWallet(walletId, password);
    }
}
