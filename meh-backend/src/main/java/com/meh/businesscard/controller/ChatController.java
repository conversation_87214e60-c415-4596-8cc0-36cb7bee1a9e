package com.meh.businesscard.controller;

import com.meh.businesscard.common.annotation.OperationLog;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.ChatMessageDTO;
import com.meh.businesscard.service.ChatService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 在线咨询控制器
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Tag(name = "在线咨询")
@RestController
@RequestMapping("/chats")
public class ChatController {

    @Autowired
    private ChatService chatService;

    /**
     * 获取我的会话列表
     *
     * @return 会话列表
     */
    @Operation(summary = "获取我的会话列表", description = "获取当前用户的所有会话列表")
    @GetMapping
    @OperationLog(module = "在线咨询", operationType = "查询", description = "获取会话列表")
    public Result<?> getMyChats() {
        return chatService.getChatList();
    }

    /**
     * 获取会话详情
     *
     * @param id 会话ID
     * @return 会话详情
     */
    @Operation(summary = "获取会话详情", description = "获取指定会话的详细信息")
    //@ApiImplicitParam(name = "id", value = "会话ID", required = true, paramType = "path", dataTypeClass = Long.class)
    @GetMapping("/{id}")
    @OperationLog(module = "在线咨询", operationType = "查询", description = "获取会话详情")
    public Result<?> getChatDetail(@PathVariable Long id) {
        return chatService.getChatDetail(id);
    }

    /**
     * 创建会话
     *
     * @param targetUserId 目标用户ID
     * @return 创建结果
     */
    @Operation(summary = "创建会话", description = "与指定用户创建新的会话")
    //@ApiImplicitParam(name = "targetUserId", value = "目标用户ID", required = true, paramType = "query", dataTypeClass = Long.class)
    @PostMapping
    @OperationLog(module = "在线咨询", operationType = "新增", description = "创建会话")
    public Result<?> createChat(@RequestParam Long targetUserId) {
        return chatService.createChat(targetUserId);
    }

    /**
     * 获取会话消息列表
     *
     * @param chatId 会话ID
     * @param page 页码
     * @param size 每页大小
     * @return 消息列表
     */
    @Operation(summary = "获取会话消息列表", description = "分页获取指定会话的消息历史记录")
    @GetMapping("/{chatId}/messages")
    @OperationLog(module = "在线咨询", operationType = "查询", description = "获取会话消息列表")
    public Result<?> getChatMessages(
            @PathVariable Long chatId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        return chatService.getChatMessageList(chatId, page, size);
    }

    /**
     * 发送消息
     *
     * @param messageDTO 消息参数
     * @return 发送结果
     */
    @Operation(summary = "发送消息", description = "在指定会话中发送消息")
    @PostMapping("/messages")
    @OperationLog(module = "在线咨询", operationType = "新增", description = "发送消息")
    public Result<?> sendMessage(@RequestBody @Validated ChatMessageDTO messageDTO) {
        return chatService.sendMessage(messageDTO);
    }

    /**
     * 标记消息已读
     *
     * @param chatId 会话ID
     * @return 标记结果
     */
    @Operation(summary = "标记消息已读", description = "将指定会话的消息标记为已读")
    //@ApiImplicitParam(name = "chatId", value = "会话ID", required = true, paramType = "path", dataTypeClass = Long.class)
    @PutMapping("/{chatId}/read")
    @OperationLog(module = "在线咨询", operationType = "更新", description = "标记消息已读")
    public Result<?> markAsRead(@PathVariable Long chatId) {
        return chatService.markChatRead(chatId);
    }

    /**
     * 删除会话
     *
     * @param id 会话ID
     * @return 删除结果
     */
    @Operation(summary = "删除会话", description = "删除指定的会话记录")
    //@ApiImplicitParam(name = "id", value = "会话ID", required = true, paramType = "path", dataTypeClass = Long.class)
    @DeleteMapping("/{id}")
    @OperationLog(module = "在线咨询", operationType = "删除", description = "删除会话")
    public Result<?> deleteChat(@PathVariable Long id) {
        return chatService.deleteChat(id);
    }
}
