package com.meh.businesscard.controller;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.*;
import com.meh.businesscard.service.WalletService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 区块链钱包管理控制器
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Tag(name = "钱包管理")
@RestController
@RequestMapping("/wallets")
@RequiredArgsConstructor
public class WalletController {

    private final WalletService walletService;

    /**
     * 创建钱包
     */
    @Operation(summary = "创建钱包", description = "支持HD钱包、导入钱包、硬件钱包")
    @PostMapping
    public Result<?> createWallet(@RequestBody @Validated WalletCreateDTO dto) {
        return walletService.createWallet(dto);
    }

    /**
     * 获取用户钱包列表
     */
    @Operation(summary = "获取钱包列表", description = "获取当前用户的所有钱包")
    @GetMapping
    public Result<?> getWalletList() {
        return walletService.getWalletList();
    }

    /**
     * 获取钱包详情
     */
    @Operation(summary = "获取钱包详情", description = "获取指定钱包的详细信息")
    @GetMapping("/{walletId}")
    public Result<?> getWalletDetail(@PathVariable Long walletId) {
        return walletService.getWalletDetail(walletId);
    }

    /**
     * 更新钱包信息
     */
    @Operation(summary = "更新钱包信息", description = "更新钱包名称、标签等信息")
    @PutMapping("/{walletId}")
    public Result<?> updateWallet(@PathVariable Long walletId, @RequestBody WalletUpdateDTO dto) {
        return walletService.updateWallet(walletId, dto);
    }

    /**
     * 删除钱包
     */
    @Operation(summary = "删除钱包", description = "删除指定钱包（需要验证密码）")
    @DeleteMapping("/{walletId}")
    public Result<?> deleteWallet(@PathVariable Long walletId, @RequestParam String password) {
        return walletService.deleteWallet(walletId, password);
    }

    /**
     * 恢复钱包
     */
    @Operation(summary = "恢复钱包", description = "通过助记词或私钥恢复钱包")
    @PostMapping("/recover")
    public Result<?> recoverWallet(@RequestBody @Validated WalletRecoverDTO dto) {
        return walletService.recoverWallet(dto);
    }

    /**
     * 获取钱包余额
     */
    @Operation(summary = "获取钱包余额", description = "获取钱包各币种余额及法币价值")
    @GetMapping("/{walletId}/balance")
    public Result<?> getWalletBalance(@PathVariable Long walletId) {
        return walletService.getWalletBalance(walletId);
    }

    /**
     * 刷新钱包余额
     */
    @Operation(summary = "刷新钱包余额", description = "主动刷新钱包余额信息")
    @PostMapping("/{walletId}/refresh")
    public Result<?> refreshWalletBalance(@PathVariable Long walletId) {
        return walletService.refreshWalletBalance(walletId);
    }

    /**
     * 获取资产组合统计
     */
    @Operation(summary = "获取资产组合", description = "获取钱包资产分布和价值统计")
    @GetMapping("/{walletId}/portfolio")
    public Result<?> getWalletPortfolio(@PathVariable Long walletId) {
        return walletService.getWalletPortfolio(walletId);
    }

    /**
     * 设置主钱包
     */
    @Operation(summary = "设置主钱包", description = "将指定钱包设置为主钱包")
    @PostMapping("/{walletId}/set-main")
    public Result<?> setMainWallet(@PathVariable Long walletId) {
        return walletService.setMainWallet(walletId);
    }

    /**
     * 生成助记词
     */
    @Operation(summary = "生成助记词", description = "生成12/24位助记词用于创建HD钱包")
    @PostMapping("/generate-mnemonic")
    public Result<?> generateMnemonic(@RequestParam(defaultValue = "12") Integer wordCount) {
        return walletService.generateMnemonic(wordCount);
    }

    /**
     * 验证助记词
     */
    @Operation(summary = "验证助记词", description = "验证助记词的有效性")
    @PostMapping("/validate-mnemonic")
    public Result<?> validateMnemonic(@RequestParam String mnemonic) {
        return walletService.validateMnemonic(mnemonic);
    }

    /**
     * 获取支持的币种列表
     */
    @Operation(summary = "获取支持币种", description = "获取平台支持的所有币种信息")
    @GetMapping("/supported-coins")
    public Result<?> getSupportedCoins() {
        return walletService.getSupportedCoins();
    }

    /**
     * 与名片系统关联
     */
    @Operation(summary = "关联名片", description = "将钱包地址关联到指定名片")
    @PostMapping("/{walletId}/bind-card")
    public Result<?> bindToCard(@PathVariable Long walletId, @RequestBody Map<String, Object> request) {
        Long cardId = Long.valueOf(request.get("cardId").toString());
        return walletService.bindToCard(walletId, cardId);
    }
}
