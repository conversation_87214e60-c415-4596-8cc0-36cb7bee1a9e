package com.meh.businesscard.controller;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.*;
import com.meh.businesscard.service.WalletTransactionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 钱包交易控制器
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Tag(name = "钱包交易")
@RestController
@RequestMapping("/wallet-transactions")
@RequiredArgsConstructor
public class WalletTransactionController {

    private final WalletTransactionService transactionService;

    /**
     * 发起转账
     */
    @Operation(summary = "发起转账", description = "支持普通转账、预约转账、批量转账")
    @PostMapping("/transfer")
    public Result<?> transfer(@RequestBody @Validated TransferDTO dto) {
        return transactionService.transfer(dto);
    }

    /**
     * 批量转账
     */
    @Operation(summary = "批量转账", description = "一次性向多个地址发送代币")
    @PostMapping("/batch-transfer")
    public Result<?> batchTransfer(@RequestBody @Validated BatchTransferDTO dto) {
        return transactionService.batchTransfer(dto);
    }

    /**
     * 生成收款二维码
     */
    @Operation(summary = "生成收款码", description = "生成收款二维码和分享链接")
    @PostMapping("/generate-payment-qr")
    public Result<?> generatePaymentQR(@RequestBody @Validated ReceivePaymentDTO dto) {
        return transactionService.generatePaymentQR(dto);
    }

    /**
     * 获取交易记录
     */
    @Operation(summary = "获取交易记录", description = "获取钱包交易历史记录")
    @GetMapping("/history")
    public Result<?> getTransactionHistory(
            @RequestParam(required = false) Long walletId,
            @RequestParam(required = false) String txType,
            @RequestParam(required = false) String coinType,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        return transactionService.getTransactionHistory(walletId, txType, coinType, startTime, endTime, pageNum, pageSize);
    }

    /**
     * 获取交易详情
     */
    @Operation(summary = "获取交易详情", description = "获取指定交易的详细信息")
    @GetMapping("/{txId}")
    public Result<?> getTransactionDetail(@PathVariable Long txId) {
        return transactionService.getTransactionDetail(txId);
    }

    /**
     * 取消交易
     */
    @Operation(summary = "取消交易", description = "取消待确认的交易（支付更高Gas费）")
    @PostMapping("/{txId}/cancel")
    public Result<?> cancelTransaction(@PathVariable Long txId, @RequestParam String password) {
        return transactionService.cancelTransaction(txId, password);
    }

    /**
     * 加速交易
     */
    @Operation(summary = "加速交易", description = "提高Gas价格加速交易确认")
    @PostMapping("/{txId}/speedup")
    public Result<?> speedUpTransaction(@PathVariable Long txId, @RequestParam String newGasPrice, @RequestParam String password) {
        return transactionService.speedUpTransaction(txId, newGasPrice, password);
    }

    /**
     * 估算Gas费用
     */
    @Operation(summary = "估算Gas费用", description = "预估交易所需的Gas费用")
    @PostMapping("/estimate-gas")
    public Result<?> estimateGas(@RequestBody GasEstimateDTO dto) {
        return transactionService.estimateGas(dto);
    }

    /**
     * 导出交易记录
     */
    @Operation(summary = "导出交易记录", description = "导出CSV或PDF格式的交易记录")
    @PostMapping("/export")
    public Result<?> exportTransactions(@RequestBody TransactionExportDTO dto) {
        return transactionService.exportTransactions(dto);
    }

    /**
     * 获取收款记录
     */
    @Operation(summary = "获取收款记录", description = "获取收款历史和状态")
    @GetMapping("/payment-requests")
    public Result<?> getPaymentRequests(
            @RequestParam(required = false) Long walletId,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        return transactionService.getPaymentRequests(walletId, status, pageNum, pageSize);
    }

    /**
     * 搜索交易
     */
    @Operation(summary = "搜索交易", description = "按关键词搜索交易记录")
    @GetMapping("/search")
    public Result<?> searchTransactions(
            @RequestParam String keyword,
            @RequestParam(required = false) Long walletId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        return transactionService.searchTransactions(keyword, walletId, pageNum, pageSize);
    }
}
