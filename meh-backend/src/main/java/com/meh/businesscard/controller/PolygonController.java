package com.meh.businesscard.controller;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.service.PolygonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

/**
 * Polygon区块链控制器
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Slf4j
@RestController
@RequestMapping("/polygon")
@RequiredArgsConstructor
@Tag(name = "Polygon区块链", description = "Polygon网络相关接口")
public class PolygonController {

    private final PolygonService polygonService;

    /**
     * 生成Polygon钱包地址
     */
    @Operation(summary = "生成钱包地址", description = "从助记词生成Polygon钱包地址")
    @PostMapping("/generate-address")
    public Result<?> generateWalletAddress(@RequestParam String mnemonic,
                                         @RequestParam(required = false) String password) {
        return polygonService.generateWalletAddress(mnemonic, password);
    }

    /**
     * 获取MATIC余额
     */
    @Operation(summary = "获取MATIC余额", description = "查询指定地址的MATIC余额")
    @GetMapping("/balance/matic/{address}")
    public Result<BigDecimal> getMaticBalance(@PathVariable String address) {
        return polygonService.getMaticBalance(address);
    }

    /**
     * 获取Token余额
     */
    @Operation(summary = "获取Token余额", description = "查询指定地址的Token余额")
    @GetMapping("/balance/token/{address}")
    public Result<BigDecimal> getTokenBalance(@PathVariable String address,
                                            @RequestParam String contractAddress) {
        return polygonService.getTokenBalance(address, contractAddress);
    }

    /**
     * 发送MATIC转账
     */
    @Operation(summary = "发送MATIC转账", description = "发送MATIC到指定地址")
    @PostMapping("/transfer/matic")
    public Result<String> sendMatic(@RequestParam String fromAddress,
                                  @RequestParam String toAddress,
                                  @RequestParam BigDecimal amount,
                                  @RequestParam String privateKey) {
        return polygonService.sendMatic(fromAddress, toAddress, amount, privateKey);
    }

    /**
     * 发送Token转账
     */
    @Operation(summary = "发送Token转账", description = "发送Token到指定地址")
    @PostMapping("/transfer/token")
    public Result<String> sendToken(@RequestParam String fromAddress,
                                  @RequestParam String toAddress,
                                  @RequestParam BigDecimal amount,
                                  @RequestParam String contractAddress,
                                  @RequestParam String privateKey) {
        return polygonService.sendToken(fromAddress, toAddress, amount, contractAddress, privateKey);
    }

    /**
     * 估算Gas费用
     */
    @Operation(summary = "估算Gas费用", description = "估算交易所需的Gas费用")
    @PostMapping("/estimate-gas")
    public Result<?> estimateGasFee(@RequestParam String fromAddress,
                                   @RequestParam String toAddress,
                                   @RequestParam BigDecimal amount,
                                   @RequestParam(required = false) String contractAddress) {
        return polygonService.estimateGasFee(fromAddress, toAddress, amount, contractAddress);
    }

    /**
     * 获取交易详情
     */
    @Operation(summary = "获取交易详情", description = "根据交易哈希获取交易详情")
    @GetMapping("/transaction/{txHash}")
    public Result<?> getTransactionDetails(@PathVariable String txHash) {
        return polygonService.getTransactionDetails(txHash);
    }

    /**
     * 获取交易历史
     */
    @Operation(summary = "获取交易历史", description = "获取指定地址的交易历史")
    @GetMapping("/transactions/{address}")
    public Result<?> getTransactionHistory(@PathVariable String address,
                                         @RequestParam(defaultValue = "1") int page,
                                         @RequestParam(defaultValue = "20") int size) {
        return polygonService.getTransactionHistory(address, page, size);
    }

    /**
     * 验证地址格式
     */
    @Operation(summary = "验证地址格式", description = "验证钱包地址格式是否正确")
    @GetMapping("/validate-address/{address}")
    public Result<Boolean> validateAddress(@PathVariable String address) {
        boolean isValid = polygonService.isValidAddress(address);
        return Result.success(isValid);
    }

    /**
     * 获取当前Gas价格
     */
    @Operation(summary = "获取Gas价格", description = "获取当前网络的Gas价格")
    @GetMapping("/gas-price")
    public Result<?> getCurrentGasPrice() {
        return polygonService.getCurrentGasPrice();
    }

    /**
     * 获取网络状态
     */
    @Operation(summary = "获取网络状态", description = "获取Polygon网络状态信息")
    @GetMapping("/network-status")
    public Result<?> getNetworkStatus() {
        return polygonService.getNetworkStatus();
    }

    /**
     * 部署MEH Token合约
     */
    @Operation(summary = "部署MEH Token", description = "部署项目专用Token合约")
    @PostMapping("/deploy-meh-token")
    public Result<String> deployMehToken(@RequestParam String deployerPrivateKey) {
        return polygonService.deployMehToken(deployerPrivateKey);
    }

    /**
     * 铸造MEH Token
     */
    @Operation(summary = "铸造MEH Token", description = "铸造MEH Token到指定地址")
    @PostMapping("/mint-meh-token")
    public Result<String> mintMehToken(@RequestParam String toAddress,
                                     @RequestParam BigDecimal amount,
                                     @RequestParam String ownerPrivateKey) {
        return polygonService.mintMehToken(toAddress, amount, ownerPrivateKey);
    }

    /**
     * 获取MEH Token信息
     */
    @Operation(summary = "获取MEH Token信息", description = "获取项目Token的基本信息")
    @GetMapping("/meh-token-info")
    public Result<?> getMehTokenInfo() {
        return polygonService.getMehTokenInfo();
    }
}
