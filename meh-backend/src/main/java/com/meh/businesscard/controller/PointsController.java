package com.meh.businesscard.controller;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.entity.UserPoints;
import com.meh.businesscard.entity.PointsGoods;
import com.meh.businesscard.entity.PointsExchange;
import com.meh.businesscard.dto.PointsExchangeDTO;
import com.meh.businesscard.service.UserPointsService;
import com.meh.businesscard.service.PointsGoodsService;
import com.meh.businesscard.service.PointsExchangeService;
import com.meh.businesscard.service.UserService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 积分控制器
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Tag(name = "积分管理")
@RestController
@RequestMapping("/points")
public class PointsController {

    @Autowired
    private UserPointsService userPointsService;

    @Autowired
    private PointsGoodsService pointsGoodsService;

    @Autowired
    private PointsExchangeService pointsExchangeService;

    @Autowired
    private UserService userService;
    
    /**
     * 获取我的积分信息
     * 
     * @return 积分信息
     */
    @Operation(summary = "获取我的积分信息", description = "获取当前用户的积分详细信息")
    @GetMapping("/my")
    public Result<UserPoints> getMyPoints() {
        Long userId = userService.getCurrentUser().getId();
        UserPoints userPoints = userPointsService.getUserPoints(userId);
        return Result.success(userPoints);
    }
    
    /**
     * 每日签到
     *
     * @return 签到结果
     */
    @Operation(summary = "每日签到", description = "用户每日签到获取积分奖励")
    @PostMapping("/checkin")
    public Result<?> dailyCheckin() {
        Long userId = userService.getCurrentUser().getId();
        return userPointsService.dailyCheckin(userId);
    }

    /**
     * 获取我的积分变动记录
     *
     * @param page 页码
     * @param size 每页大小
     * @return 积分变动记录
     */
    @Operation(summary = "获取我的积分变动记录", description = "分页获取当前用户的积分变动历史记录")
    @GetMapping("/logs")
    public Result<?> getMyPointsLogs(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        Long userId = userService.getCurrentUser().getId();
        java.util.List<com.meh.businesscard.entity.UserPointsLog> logs = userPointsService.getPointsLogs(userId, page, size);
        return Result.success(logs);
    }

    /**
     * 获取积分商品列表
     */
    @Operation(summary = "获取积分商品列表", description = "获取积分商城的商品列表，支持分类筛选")
    @GetMapping("/goods")
    public Result<?> listGoods(@RequestParam(required = false) Long categoryId) {
        java.util.List<PointsGoods> list = pointsGoodsService.listGoods(categoryId);
        return Result.success(list);
    }

    /**
     * 获取积分商城商品列表（兼容前端调用）
     */
    @Operation(summary = "获取积分商城商品列表", description = "获取积分商城的商品列表（兼容接口）")
    @GetMapping("/mall")
    public Result<?> getMallGoods(@RequestParam(required = false) Long categoryId) {
        java.util.List<PointsGoods> list = pointsGoodsService.listGoods(categoryId);
        return Result.success(list);
    }

    /**
     * 兑换积分商品
     */
    @Operation(summary = "兑换积分商品", description = "使用积分兑换指定商品")
    @PostMapping("/exchange")
    public Result<?> exchange(@RequestBody PointsExchangeDTO dto) {
        Long userId = userService.getCurrentUser().getId();
        PointsExchange exchange = pointsExchangeService.exchange(userId, dto.getGoodsId(), dto.getQuantity());
        return Result.success(exchange);
    }
}
