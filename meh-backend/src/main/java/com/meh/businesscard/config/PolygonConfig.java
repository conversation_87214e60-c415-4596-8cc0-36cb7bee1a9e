package com.meh.businesscard.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Polygon区块链网络配置
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "polygon")
public class PolygonConfig {

    /**
     * 主网配置
     */
    private NetworkConfig mainnet = new NetworkConfig();

    /**
     * 测试网配置
     */
    private NetworkConfig testnet = new NetworkConfig();

    /**
     * 默认网络 (mainnet/testnet)
     */
    private String defaultNetwork = "testnet";

    /**
     * Gas配置
     */
    private GasConfig gas = new GasConfig();

    /**
     * MEH Token配置
     */
    private TokenConfig mehToken = new TokenConfig();

    /**
     * 网络配置类
     */
    @Data
    public static class NetworkConfig {
        /**
         * RPC节点URL
         */
        private String rpcUrl;

        /**
         * 链ID
         */
        private Long chainId;

        /**
         * 网络名称
         */
        private String networkName;

        /**
         * 货币符号
         */
        private String currencySymbol;

        /**
         * 货币精度
         */
        private Integer currencyDecimals;

        /**
         * 区块浏览器URL
         */
        private String blockExplorer;
    }

    /**
     * Gas配置类
     */
    @Data
    public static class GasConfig {
        /**
         * 默认Gas价格 (Gwei)
         */
        private Long defaultGasPrice = 30L;

        /**
         * 最大Gas价格 (Gwei)
         */
        private Long maxGasPrice = 100L;

        /**
         * 默认Gas限制
         */
        private Long defaultGasLimit = 21000L;

        /**
         * 合约调用Gas限制
         */
        private Long contractGasLimit = 200000L;
    }

    /**
     * Token配置类
     */
    @Data
    public static class TokenConfig {
        /**
         * Token名称
         */
        private String name;

        /**
         * Token符号
         */
        private String symbol;

        /**
         * Token精度
         */
        private Integer decimals;

        /**
         * 初始供应量
         */
        private Long initialSupply;

        /**
         * 合约地址
         */
        private String contractAddress;
    }

    /**
     * 获取当前使用的网络配置
     */
    public NetworkConfig getCurrentNetwork() {
        if ("mainnet".equals(defaultNetwork)) {
            return mainnet;
        } else {
            return testnet;
        }
    }

    /**
     * 是否为主网
     */
    public boolean isMainnet() {
        return "mainnet".equals(defaultNetwork);
    }

    /**
     * 是否为测试网
     */
    public boolean isTestnet() {
        return "testnet".equals(defaultNetwork);
    }
}
