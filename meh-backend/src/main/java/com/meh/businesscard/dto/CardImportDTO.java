package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 从名片导入地址DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class CardImportDTO implements Serializable {

    /**
     * 要导入的名片ID列表
     */
    @NotEmpty(message = "名片ID列表不能为空")
    private List<Long> cardIds;

    /**
     * 导入的币种类型（可选，为空表示导入所有币种）
     */
    private List<String> coinTypes;

    /**
     * 是否覆盖已存在的地址
     */
    private Boolean overwrite = false;

    /**
     * 默认地址分类
     */
    private String defaultCategory = "BUSINESS";

    /**
     * 默认地址标签
     */
    private String defaultLabel;
}
