package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 钱包创建DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class WalletCreateDTO implements Serializable {

    /**
     * 钱包名称
     */
    @NotBlank(message = "钱包名称不能为空")
    private String walletName;

    /**
     * 钱包类型：HD、IMPORT、HARDWARE
     */
    @NotBlank(message = "钱包类型不能为空")
    private String walletType;

    /**
     * 币种类型：ETH、BTC、USDT等
     */
    @NotBlank(message = "币种类型不能为空")
    private String coinType;

    /**
     * 网络类型：MAINNET、TESTNET
     */
    @NotBlank(message = "网络类型不能为空")
    private String networkType;

    /**
     * 私钥（仅导入钱包时需要）
     */
    private String privateKey;

    /**
     * 助记词（仅HD钱包时需要）
     */
    private String mnemonic;

    /**
     * 钱包密码
     */
    @NotBlank(message = "钱包密码不能为空")
    private String password;

    /**
     * 是否设为主钱包
     */
    private Boolean isMain = false;

    /**
     * 关联名片ID（可选）
     */
    private Long cardId;
}
