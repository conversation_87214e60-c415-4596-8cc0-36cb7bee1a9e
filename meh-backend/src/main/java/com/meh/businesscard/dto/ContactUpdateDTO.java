package com.meh.businesscard.dto;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 联系人更新参数
 * 
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
//@ApiModel(value = "联系人更新参数")
public class ContactUpdateDTO {
    
    ////@ApiModelProperty(value = "联系人ID", required = true)
    @NotNull(message = "联系人ID不能为空")
    private Long id;
    
    ////@ApiModelProperty(value = "联系人姓名", required = true)
    @NotBlank(message = "联系人姓名不能为空")
    private String name;
    
    ////@ApiModelProperty(value = "联系人职位")
    private String position;
    
    ////@ApiModelProperty(value = "联系人公司")
    private String company;
    
    ////@ApiModelProperty(value = "联系人电话")
    private String phone;
    
    ////@ApiModelProperty(value = "联系人邮箱")
    private String email;
    
    ////@ApiModelProperty(value = "联系人地址")
    private String address;
    
    ////@ApiModelProperty(value = "联系人头像")
    private String avatar;
    
    ////@ApiModelProperty(value = "联系人备注")
    private String remark;
    
    ////@ApiModelProperty(value = "分组ID")
    private Long groupId;
    
    ////@ApiModelProperty(value = "标签，JSON格式存储")
    private String tags;
    
    ////@ApiModelProperty(value = "关联的名片ID")
    private Long cardId;
}
