package com.meh.businesscard.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易导出DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class TransactionExportDTO implements Serializable {

    /**
     * 钱包ID列表（可选，为空表示导出所有钱包）
     */
    private List<Long> walletIds;

    /**
     * 币种类型列表（可选，为空表示导出所有币种）
     */
    private List<String> coinTypes;

    /**
     * 交易类型列表（可选，为空表示导出所有类型）
     * TRANSFER_IN - 转入
     * TRANSFER_OUT - 转出
     * RECEIVE - 收款
     * SEND - 发送
     */
    private List<String> transactionTypes;

    /**
     * 交易状态列表（可选，为空表示导出所有状态）
     * PENDING - 待确认
     * CONFIRMED - 已确认
     * FAILED - 失败
     * CANCELLED - 已取消
     */
    private List<String> statuses;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 导出格式：CSV、EXCEL、PDF
     */
    private String format = "CSV";

    /**
     * 是否包含详细信息
     */
    private Boolean includeDetails = true;

    /**
     * 是否包含Gas费信息
     */
    private Boolean includeGasFee = true;

    /**
     * 排序方式：TIME_ASC、TIME_DESC、AMOUNT_ASC、AMOUNT_DESC
     */
    private String sortBy = "TIME_DESC";

    /**
     * 最大导出记录数
     */
    private Integer maxRecords = 10000;
}
