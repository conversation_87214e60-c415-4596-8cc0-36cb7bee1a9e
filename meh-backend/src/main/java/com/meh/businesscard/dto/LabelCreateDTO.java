package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 地址标签创建DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class LabelCreateDTO implements Serializable {

    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不能为空")
    @Size(max = 20, message = "标签名称不能超过20个字符")
    private String labelName;

    /**
     * 标签颜色（十六进制颜色值）
     */
    private String labelColor = "#007AFF";

    /**
     * 标签图标
     */
    private String labelIcon;

    /**
     * 标签描述
     */
    @Size(max = 100, message = "标签描述不能超过100个字符")
    private String description;

    /**
     * 标签分类：PERSONAL、BUSINESS、EXCHANGE、CUSTOM
     */
    private String category = "CUSTOM";

    /**
     * 是否为系统标签
     */
    private Boolean isSystem = false;

    /**
     * 排序号
     */
    private Integer sort = 0;
}
