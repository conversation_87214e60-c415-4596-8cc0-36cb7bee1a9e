package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 地址簿更新DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class AddressBookUpdateDTO implements Serializable {

    /**
     * 联系人名称
     */
    @NotBlank(message = "联系人名称不能为空")
    private String contactName;

    /**
     * 钱包地址
     */
    @NotBlank(message = "钱包地址不能为空")
    private String walletAddress;

    /**
     * 币种类型
     */
    @NotBlank(message = "币种类型不能为空")
    private String coinType;

    /**
     * 地址标签
     */
    private String addressLabel;

    /**
     * 地址分类：PERSONAL、BUSINESS、EXCHANGE
     */
    private String addressCategory = "PERSONAL";

    /**
     * 地址备注
     */
    private String remark;

    /**
     * 关联的名片ID（可选）
     */
    private Long cardId;

    /**
     * 关联的联系人ID（可选）
     */
    private Long contactId;

    /**
     * 是否设为常用地址
     */
    private Boolean isFrequent = false;

    /**
     * 风险等级：LOW、MEDIUM、HIGH
     */
    private String riskLevel = "LOW";
}
