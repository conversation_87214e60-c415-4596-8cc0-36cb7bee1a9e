package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import javax.validation.constraints.DecimalMin;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 冷却期设置DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class CoolingPeriodDTO implements Serializable {

    /**
     * 钱包ID（可选，为空表示设置全局冷却期）
     */
    private Long walletId;

    /**
     * 是否启用冷却期
     */
    @NotNull(message = "冷却期状态不能为空")
    private Boolean enabled;

    /**
     * 冷却期时长（小时）
     */
    @Min(value = 1, message = "冷却期时长不能小于1小时")
    @Max(value = 168, message = "冷却期时长不能大于168小时（7天）")
    private Integer coolingPeriodHours;

    /**
     * 触发冷却期的交易金额阈值
     */
    @DecimalMin(value = "0.0", message = "交易金额阈值不能小于0")
    private BigDecimal triggerAmount;

    /**
     * 币种类型（可选，为空表示适用于所有币种）
     */
    private String coinType;

    /**
     * 冷却期类型：LARGE_AMOUNT（大额交易）、SUSPICIOUS（可疑交易）、MANUAL（手动触发）
     */
    private String coolingType = "LARGE_AMOUNT";

    /**
     * 是否允许紧急取消冷却期
     */
    private Boolean allowEmergencyCancel = false;

    /**
     * 交易密码（用于验证身份）
     */
    private String transactionPassword;

    /**
     * 备注信息
     */
    private String remark;
}
