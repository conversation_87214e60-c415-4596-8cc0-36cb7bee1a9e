package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 交易限额设置DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class TransactionLimitsDTO implements Serializable {

    /**
     * 钱包ID（可选，为空表示设置全局限额）
     */
    private Long walletId;

    /**
     * 日交易限额
     */
    @DecimalMin(value = "0.0", message = "日交易限额不能小于0")
    private BigDecimal dailyLimit;

    /**
     * 月交易限额
     */
    @DecimalMin(value = "0.0", message = "月交易限额不能小于0")
    private BigDecimal monthlyLimit;

    /**
     * 单笔交易限额
     */
    @DecimalMin(value = "0.0", message = "单笔交易限额不能小于0")
    private BigDecimal singleTransactionLimit;

    /**
     * 大额交易阈值（超过此金额需要额外验证）
     */
    @DecimalMin(value = "0.0", message = "大额交易阈值不能小于0")
    private BigDecimal largeAmountThreshold;

    /**
     * 币种类型（可选，为空表示适用于所有币种）
     */
    private String coinType;

    /**
     * 是否启用限额控制
     */
    @NotNull(message = "限额控制状态不能为空")
    private Boolean enabled;

    /**
     * 交易密码（用于验证身份）
     */
    private String transactionPassword;

    /**
     * 备注信息
     */
    private String remark;
}
