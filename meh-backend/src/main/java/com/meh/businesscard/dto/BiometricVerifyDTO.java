package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 生物识别验证DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class BiometricVerifyDTO implements Serializable {

    /**
     * 生物识别类型：FINGERPRINT、FACE_ID、VOICE
     */
    @NotBlank(message = "生物识别类型不能为空")
    private String biometricType;

    /**
     * 生物识别数据（当前验证的特征数据）
     */
    @NotBlank(message = "生物识别数据不能为空")
    private String biometricData;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 验证场景：LOGIN、TRANSACTION、SETTINGS
     */
    private String verificationScene;

    /**
     * 关联的交易ID（如果是交易验证）
     */
    private Long transactionId;

    /**
     * 时间戳（防重放攻击）
     */
    private Long timestamp;

    /**
     * 随机数（防重放攻击）
     */
    private String nonce;

    /**
     * 客户端签名（可选，用于增强安全性）
     */
    private String clientSignature;
}
