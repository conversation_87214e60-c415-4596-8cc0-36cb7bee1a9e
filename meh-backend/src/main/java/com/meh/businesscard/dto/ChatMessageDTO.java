package com.meh.businesscard.dto;


import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

/**
 * 聊天消息参数
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
//@ApiModel(value = "聊天消息参数")
public class ChatMessageDTO {

    ////@ApiModelProperty(value = "会话ID", required = true, example = "123")
    @NotNull(message = "会���ID不能为空")
    @Positive(message = "会话ID必须是正数")
    private Long chatId;

    ////@ApiModelProperty(value = "消息内容", required = true, example = "您好，请问有什么可以帮您的？")
    @NotBlank(message = "消息内容不能为空")
    @Length(max = 2000, message = "消息内容长度不能超过2000个字符")
    private String content;

    ////@ApiModelProperty(value = "消息类型：1-文本，2-图片，3-语音，4-视频", required = true, example = "1", allowableValues = "1,2,3,4")
    @NotNull(message = "消息类型不能为空")
    @Min(value = 1, message = "消息类型必须在1-4之间")
    @Max(value = 4, message = "消息类型必须在1-4之间")
    private Integer type;

    ////@ApiModelProperty(value = "接收者ID", example = "456")
    @Positive(message = "接收者ID必须是正数")
    private Long receiverId;

    ////@ApiModelProperty(value = "媒体文件URL", example = "https://example.com/media/image.jpg")
    @Length(max = 255, message = "媒体文件URL长度不能超过255个字符")
    private String mediaUrl;

    ////@ApiModelProperty(value = "媒体文件大小(kb)", example = "1024")
    @PositiveOrZero(message = "媒体文件大小必须是非负数")
    private Integer mediaSize;

    ////@ApiModelProperty(value = "媒体文件时长(秒)", example = "60")
    @PositiveOrZero(message = "媒体文件时长必须是非负数")
    private Integer mediaDuration;
}
