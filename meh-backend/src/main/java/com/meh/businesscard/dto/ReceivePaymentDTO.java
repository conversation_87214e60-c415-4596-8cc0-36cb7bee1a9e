package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 收款DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class ReceivePaymentDTO implements Serializable {

    /**
     * 收款钱包ID
     */
    private Long walletId;

    /**
     * 收款金额（可选，用于生成固定金额二维码）
     */
    private BigDecimal amount;

    /**
     * 币种类型
     */
    @NotBlank(message = "币种类型不能为空")
    private String coinType;

    /**
     * 收款备注
     */
    private String memo;

    /**
     * 二维码有效期（分钟）
     */
    private Integer expiryMinutes = 30;

    /**
     * 关联名片ID（可选）
     */
    private Long cardId;

    /**
     * 是否生成分享链接
     */
    private Boolean generateShareLink = false;

    /**
     * 分享链接标题
     */
    private String shareTitle;

    /**
     * 分享链接描述
     */
    private String shareDescription;
}
