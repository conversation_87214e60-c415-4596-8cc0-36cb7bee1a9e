package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 钱包更新DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class WalletUpdateDTO implements Serializable {

    /**
     * 钱包名称
     */
    @NotBlank(message = "钱包名称不能为空")
    private String walletName;

    /**
     * 是否设为主钱包
     */
    private Boolean isMain;

    /**
     * 钱包状态：ACTIVE、LOCKED、DISABLED
     */
    private String status;

    /**
     * 备注信息
     */
    private String remark;
}
