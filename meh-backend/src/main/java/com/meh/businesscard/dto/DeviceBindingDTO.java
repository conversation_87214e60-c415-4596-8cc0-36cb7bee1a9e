package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 设备绑定DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class DeviceBindingDTO implements Serializable {

    /**
     * 钱包ID（可选，为空表示绑定到用户账户）
     */
    private Long walletId;

    /**
     * 设备指纹（唯一标识）
     */
    @NotBlank(message = "设备指纹不能为空")
    private String deviceFingerprint;

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空")
    private String deviceName;

    /**
     * 设备类型：MOBILE、TABLET、DESKTOP
     */
    @NotBlank(message = "设备类型不能为空")
    private String deviceType;

    /**
     * 设备操作系统
     */
    private String deviceOS;

    /**
     * 设备品牌
     */
    private String deviceBrand;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 应用版本
     */
    private String appVersion;

    /**
     * 是否启用设备绑定
     */
    @NotNull(message = "设备绑定状态不能为空")
    private Boolean enabled;

    /**
     * 是否为可信设备
     */
    private Boolean isTrusted = false;

    /**
     * 交易密码（用于验证身份）
     */
    private String transactionPassword;

    /**
     * 备注信息
     */
    private String remark;
}
