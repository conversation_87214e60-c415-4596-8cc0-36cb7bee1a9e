package com.meh.businesscard.dto;


import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 用户登录参数
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Data
//@ApiModel(value = "用户登录参数")
public class UserLoginDTO {

    ////@ApiModelProperty(value = "用户名/手机号/邮���", required = true, example = "user123")
    @NotBlank(message = "用户名不能为空")
    @Length(min = 4, max = 50, message = "用户名长度必须在4-50个字符之间")
    private String username;

    ////@ApiModelProperty(value = "密码", required = true, example = "password123")
    @NotBlank(message = "密码不能为空")
    @Length(min = 6, max = 32, message = "密码长度必须在6-32个字符之间")
    private String password;

    ////@ApiModelProperty(value = "验证码", example = "1234")
    private String verifyCode;

    ////@ApiModelProperty(value = "记住我", example = "true")
    private Boolean rememberMe = false;
}
