package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Gas费估算DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class GasEstimateDTO implements Serializable {

    /**
     * 发送方地址
     */
    @NotBlank(message = "发送方地址不能为空")
    private String fromAddress;

    /**
     * 接收方地址
     */
    @NotBlank(message = "接收方地址不能为空")
    private String toAddress;

    /**
     * 转账金额
     */
    @NotNull(message = "转账金额不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "转账金额必须大于0")
    private BigDecimal amount;

    /**
     * 币种类型
     */
    @NotBlank(message = "币种类型不能为空")
    private String coinType;

    /**
     * 合约地址（代币转账时需要）
     */
    private String contractAddress;

    /**
     * Gas价格（可选，用于自定义Gas价格）
     */
    private BigDecimal gasPrice;

    /**
     * Gas限制（可选，用于自定义Gas限制）
     */
    private Long gasLimit;

    /**
     * 交易数据（可选，用于合约调用）
     */
    private String data;

    /**
     * 优先级：LOW、MEDIUM、HIGH
     */
    private String priority = "MEDIUM";
}
