package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 交易密码DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class TransactionPasswordDTO implements Serializable {

    /**
     * 钱包ID（可选，为空表示设置全局交易密码）
     */
    private Long walletId;

    /**
     * 旧密码（修改密码时必填）
     */
    private String oldPassword;

    /**
     * 新密码
     */
    @NotBlank(message = "交易密码不能为空")
    @Size(min = 6, max = 20, message = "交易密码长度必须在6-20位之间")
    private String newPassword;

    /**
     * 确认密码
     */
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    /**
     * 密码提示问题（可选）
     */
    private String passwordHint;

    /**
     * 是否启用生物识别验证
     */
    private Boolean enableBiometric = false;

    /**
     * 生物识别类型：FINGERPRINT、FACE、VOICE
     */
    private String biometricType;
}
