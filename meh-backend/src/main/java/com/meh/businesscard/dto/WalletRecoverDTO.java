package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 钱包恢复DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class WalletRecoverDTO implements Serializable {

    /**
     * 钱包名称
     */
    @NotBlank(message = "钱包名称不能为空")
    private String walletName;

    /**
     * 恢复方式：MNEMONIC、PRIVATE_KEY、KEYSTORE
     */
    @NotBlank(message = "恢复方式不能为空")
    private String recoverType;

    /**
     * 助记词（恢复方式为MNEMONIC时必填）
     */
    private String mnemonic;

    /**
     * 私钥（恢复方式为PRIVATE_KEY时必填）
     */
    private String privateKey;

    /**
     * Keystore文件内容（恢复方式为KEYSTORE时必填）
     */
    private String keystoreContent;

    /**
     * Keystore密码（恢复方式为KEYSTORE时必填）
     */
    private String keystorePassword;

    /**
     * 币种类型：ETH、BTC、USDT等
     */
    @NotBlank(message = "币种类型不能为空")
    private String coinType;

    /**
     * 网络类型：MAINNET、TESTNET
     */
    @NotBlank(message = "网络类型不能为空")
    private String networkType;

    /**
     * 钱包密码
     */
    @NotBlank(message = "钱包密码不能为空")
    private String password;

    /**
     * 是否设为主钱包
     */
    private Boolean isMain = false;

    /**
     * 关联名片ID（可选）
     */
    private Long cardId;
}
