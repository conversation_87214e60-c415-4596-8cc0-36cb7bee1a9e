package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.io.Serializable;
import java.util.List;

/**
 * 多重签名设置DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class MultiSigSetupDTO implements Serializable {

    /**
     * 钱包ID
     */
    @NotNull(message = "钱包ID不能为空")
    private Long walletId;

    /**
     * 是否启用多重签名
     */
    @NotNull(message = "多重签名状态不能为空")
    private Boolean enabled;

    /**
     * 多重签名阈值（需要多少个签名才能执行交易）
     */
    @Min(value = 1, message = "多重签名阈值不能小于1")
    @Max(value = 10, message = "多重签名阈值不能大于10")
    private Integer threshold;

    /**
     * 多重签名参与者数量
     */
    @Min(value = 1, message = "参与者数量不能小于1")
    @Max(value = 10, message = "参与者数量不能大于10")
    private Integer participants;

    /**
     * 参与者地址列表
     */
    private List<String> participantAddresses;

    /**
     * 参与者名称列表（与地址对应）
     */
    private List<String> participantNames;

    /**
     * 多重签名合约地址（如果已部署）
     */
    private String contractAddress;

    /**
     * 交易密码（用于验证身份）
     */
    private String transactionPassword;

    /**
     * 备注信息
     */
    private String remark;
}
