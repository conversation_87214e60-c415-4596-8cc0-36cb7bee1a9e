package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 生物识别设置DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class BiometricSetupDTO implements Serializable {

    /**
     * 生物识别类型：FINGERPRINT、FACE_ID、VOICE
     */
    @NotBlank(message = "生物识别类型不能为空")
    private String biometricType;

    /**
     * 是否启用
     */
    @NotNull(message = "启用状态不能为空")
    private Boolean enabled;

    /**
     * 生物识别数据（加密后的特征数据）
     */
    private String biometricData;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 验证强度：LOW、MEDIUM、HIGH
     */
    private String verificationLevel = "MEDIUM";

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 是否作为主要验证方式
     */
    private Boolean isPrimary = false;

    /**
     * 有效期（天数，0表示永久有效）
     */
    private Integer validityDays = 0;
}
