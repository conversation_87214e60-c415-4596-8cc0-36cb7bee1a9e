package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 批量删除DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class BatchDeleteDTO implements Serializable {

    /**
     * 要删除的ID列表
     */
    @NotEmpty(message = "删除ID列表不能为空")
    private List<Long> ids;

    /**
     * 删除原因（可选）
     */
    private String reason;

    /**
     * 是否强制删除
     */
    private Boolean force = false;
}
