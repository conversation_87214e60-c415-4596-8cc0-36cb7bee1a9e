package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 转账DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class TransferDTO implements Serializable {

    /**
     * 钱包ID（兼容字段）
     */
    @NotNull(message = "钱包ID不能为空")
    private Long walletId;

    /**
     * 发送钱包ID
     */
    @NotNull(message = "发送钱包ID不能为空")
    private Long fromWalletId;

    /**
     * 发送地址
     */
    private String fromAddress;

    /**
     * 接收地址
     */
    @NotBlank(message = "接收地址不能为空")
    private String toAddress;

    /**
     * 转账金额
     */
    @NotNull(message = "转账金额不能为空")
    private BigDecimal amount;

    /**
     * 币种类型
     */
    @NotBlank(message = "币种类型不能为空")
    private String coinType;

    /**
     * Gas价格
     */
    private BigDecimal gasPrice;

    /**
     * Gas限制
     */
    private Long gasLimit;

    /**
     * Gas费用
     */
    private BigDecimal gasFee;

    /**
     * 交易备注
     */
    private String memo;

    /**
     * 交易密码
     */
    @NotBlank(message = "交易密码不能为空")
    private String transactionPassword;

    /**
     * 关联名片ID（可选）
     */
    private Long cardId;

    /**
     * 关联联系人ID（可选）
     */
    private Long contactId;

    /**
     * 是否预约转账
     */
    private Boolean isScheduled = false;

    /**
     * 预约执行时间（预约转账时必填）
     */
    private String scheduledTime;

    /**
     * 转账模板ID（使用模板时填写）
     */
    private Long templateId;
}
