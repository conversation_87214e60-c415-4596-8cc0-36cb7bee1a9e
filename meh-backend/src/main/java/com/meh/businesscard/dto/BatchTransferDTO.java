package com.meh.businesscard.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 批量转账DTO
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
public class BatchTransferDTO implements Serializable {

    /**
     * 发送钱包ID
     */
    @NotNull(message = "发送钱包ID不能为空")
    private Long fromWalletId;

    /**
     * 币种类型
     */
    @NotBlank(message = "币种类型不能为空")
    private String coinType;

    /**
     * 转账列表
     */
    @NotNull(message = "转账列表不能为空")
    @Size(min = 1, max = 100, message = "批量转账数量必须在1-100之间")
    private List<TransferItem> transferList;

    /**
     * Gas价格
     */
    private BigDecimal gasPrice;

    /**
     * Gas限制
     */
    private Long gasLimit;

    /**
     * 交易密码
     */
    @NotBlank(message = "交易密码不能为空")
    private String transactionPassword;

    /**
     * 交易备注
     */
    private String remark;

    /**
     * 是否预约转账
     */
    private Boolean isScheduled = false;

    /**
     * 预约执行时间（预约转账时必填）
     */
    private String scheduledTime;

    /**
     * 转账项目
     */
    @Data
    public static class TransferItem implements Serializable {
        /**
         * 接收地址
         */
        @NotBlank(message = "接收地址不能为空")
        private String toAddress;

        /**
         * 转账金额
         */
        @NotNull(message = "转账金额不能为空")
        private BigDecimal amount;

        /**
         * 备注
         */
        private String remark;
    }
}
