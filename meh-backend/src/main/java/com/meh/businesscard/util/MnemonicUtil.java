package com.meh.businesscard.util;

import org.web3j.crypto.MnemonicUtils;
import lombok.extern.slf4j.Slf4j;

import java.security.SecureRandom;
import java.util.Arrays;
import java.util.List;

/**
 * BIP39标准助记词工具类
 * 
 * 使用Web3j库实现标准的BIP39助记词生成和验证
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Slf4j
public class MnemonicUtil {

    /**
     * 生成标准BIP39助记词
     * 
     * @param wordCount 助记词数量 (12, 15, 18, 21, 24)
     * @return 助记词字符串
     */
    public static String generateMnemonic(int wordCount) {
        try {
            // 验证助记词数量
            if (!isValidWordCount(wordCount)) {
                throw new IllegalArgumentException("不支持的助记词数量: " + wordCount + "，支持的数量: 12, 15, 18, 21, 24");
            }

            // 计算熵的位数
            int entropyBits = (wordCount * 11 - wordCount / 3) / 8 * 8;
            
            // 生成随机熵
            byte[] entropy = new byte[entropyBits / 8];
            new SecureRandom().nextBytes(entropy);
            
            // 使用Web3j生成标准BIP39助记词
            String mnemonic = MnemonicUtils.generateMnemonic(entropy);
            
            // 验证生成的助记词数量
            String[] words = mnemonic.split(" ");
            if (words.length != wordCount) {
                log.warn("生成的助记词数量({})与期望数量({})不匹配，重新生成", words.length, wordCount);
                return generateMnemonic(wordCount);
            }
            
            log.info("成功生成{}位BIP39助记词", wordCount);
            return mnemonic;
            
        } catch (Exception e) {
            log.error("生成助记词失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成助记词失败", e);
        }
    }

    /**
     * 验证BIP39助记词
     * 
     * @param mnemonic 助记词字符串
     * @return 是否有效
     */
    public static boolean validateMnemonic(String mnemonic) {
        try {
            if (mnemonic == null || mnemonic.trim().isEmpty()) {
                return false;
            }
            
            // 使用Web3j验证BIP39助记词
            return MnemonicUtils.validateMnemonic(mnemonic.trim());
            
        } catch (Exception e) {
            log.error("验证助记词失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 将助记词转换为种子
     * 
     * @param mnemonic 助记词
     * @param passphrase 可选的密码短语
     * @return 种子字节数组
     */
    public static byte[] mnemonicToSeed(String mnemonic, String passphrase) {
        try {
            if (!validateMnemonic(mnemonic)) {
                throw new IllegalArgumentException("无效的助记词");
            }
            
            return MnemonicUtils.generateSeed(mnemonic, passphrase != null ? passphrase : "");
            
        } catch (Exception e) {
            log.error("助记词转种子失败: {}", e.getMessage(), e);
            throw new RuntimeException("助记词转种子失败", e);
        }
    }

    /**
     * 获取助记词单词列表
     * 
     * @param mnemonic 助记词字符串
     * @return 单词列表
     */
    public static List<String> getMnemonicWords(String mnemonic) {
        if (mnemonic == null || mnemonic.trim().isEmpty()) {
            throw new IllegalArgumentException("助记词不能为空");
        }
        
        return Arrays.asList(mnemonic.trim().split("\\s+"));
    }

    /**
     * 检查助记词数量是否有效
     * 
     * @param wordCount 助记词数量
     * @return 是否有效
     */
    public static boolean isValidWordCount(int wordCount) {
        return wordCount == 12 || wordCount == 15 || wordCount == 18 || wordCount == 21 || wordCount == 24;
    }

    /**
     * 获取助记词强度描述
     * 
     * @param wordCount 助记词数量
     * @return 强度描述
     */
    public static String getSecurityLevel(int wordCount) {
        switch (wordCount) {
            case 12:
                return "标准安全级别 (128位熵)";
            case 15:
                return "增强安全级别 (160位熵)";
            case 18:
                return "高安全级别 (192位熵)";
            case 21:
                return "超高安全级别 (224位熵)";
            case 24:
                return "最高安全级别 (256位熵)";
            default:
                return "未知安全级别";
        }
    }

    /**
     * 生成助记词详细信息
     * 
     * @param wordCount 助记词数量
     * @return 助记词详细信息
     */
    public static MnemonicInfo generateMnemonicInfo(int wordCount) {
        String mnemonic = generateMnemonic(wordCount);
        List<String> words = getMnemonicWords(mnemonic);
        String securityLevel = getSecurityLevel(wordCount);
        
        return new MnemonicInfo(mnemonic, words, wordCount, securityLevel);
    }

    /**
     * 助记词信息类
     */
    public static class MnemonicInfo {
        private final String mnemonic;
        private final List<String> words;
        private final int wordCount;
        private final String securityLevel;

        public MnemonicInfo(String mnemonic, List<String> words, int wordCount, String securityLevel) {
            this.mnemonic = mnemonic;
            this.words = words;
            this.wordCount = wordCount;
            this.securityLevel = securityLevel;
        }

        public String getMnemonic() { return mnemonic; }
        public List<String> getWords() { return words; }
        public int getWordCount() { return wordCount; }
        public String getSecurityLevel() { return securityLevel; }
    }
}
