package com.meh.businesscard.service;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.*;
import com.meh.businesscard.dto.BiometricSetupDTO;
import com.meh.businesscard.dto.BiometricVerifyDTO;

/**
 * 钱包安全服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface WalletSecurityService {

    /**
     * 设置交易密码
     */
    Result<?> setTransactionPassword(TransactionPasswordDTO dto);

    /**
     * 验证交易密码
     */
    Result<?> verifyTransactionPassword(String password);

    /**
     * 启用生物识别
     */
    Result<?> enableBiometric(BiometricSetupDTO dto);

    /**
     * 禁用生物识别
     */
    Result<?> disableBiometric(String password);

    /**
     * 验证生物识别
     */
    Result<?> verifyBiometric(BiometricVerifyDTO dto);

    /**
     * 设置多重签名
     */
    Result<?> setupMultiSignature(MultiSigSetupDTO dto);

    /**
     * 获取多重签名配置
     */
    Result<?> getMultiSignatureConfig(Long walletId);

    /**
     * 设置交易限额
     */
    Result<?> setTransactionLimits(TransactionLimitsDTO dto);

    /**
     * 获取交易限额
     */
    Result<?> getTransactionLimits(Long walletId);

    /**
     * 绑定设备
     */
    Result<?> bindDevice(DeviceBindingDTO dto);

    /**
     * 解绑设备
     */
    Result<?> unbindDevice(String deviceId, String password);

    /**
     * 获取绑定设备列表
     */
    Result<?> getBoundDevices();

    /**
     * 设置冷却期
     */
    Result<?> setCoolingPeriod(CoolingPeriodDTO dto);

    /**
     * 获取安全设置
     */
    Result<?> getSecuritySettings(Long walletId);

    /**
     * 安全检查
     */
    Result<?> performSecurityCheck(Long walletId);

    /**
     * 获取安全日志
     */
    Result<?> getSecurityLogs(Long walletId, Integer pageNum, Integer pageSize);

    /**
     * 重置安全设置
     */
    Result<?> resetSecuritySettings(Long walletId, String password);

    /**
     * 检查设备是否可信
     */
    Result<?> checkDeviceTrust(String deviceFingerprint);

    /**
     * 紧急冻结钱包
     */
    Result<?> emergencyFreeze(Long walletId, String reason);

    /**
     * 解冻钱包
     */
    Result<?> unfreezeWallet(Long walletId, String password);
}
