package com.meh.businesscard.service;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.AddressBookCreateDTO;
import com.meh.businesscard.dto.AddressBookUpdateDTO;
import com.meh.businesscard.dto.LabelCreateDTO;
import com.meh.businesscard.dto.BatchDeleteDTO;
import com.meh.businesscard.dto.CardImportDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 钱包地址簿服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface WalletAddressBookService {

    /**
     * 添加联系人
     */
    Result<?> addContact(AddressBookCreateDTO dto);

    /**
     * 批量删除联系人
     */
    Result<?> batchDeleteContacts(BatchDeleteDTO dto);

    /**
     * 关联名片
     */
    Result<?> bindToCard(Long contactId, Long cardId);

    /**
     * 从名片导入地址
     */
    Result<?> importFromCards(CardImportDTO dto);

    /**
     * 获取地址簿
     */
    Result<?> getAddressBook(String coinType, String category, String keyword, 
                            Integer pageNum, Integer pageSize);

    /**
     * 更新联系人
     */
    Result<?> updateContact(Long contactId, AddressBookUpdateDTO dto);

    /**
     * 删除联系人
     */
    Result<?> deleteContact(Long contactId);

    /**
     * 获取联系人详情
     */
    Result<?> getContactDetail(Long contactId);

    /**
     * 获取最近联系人
     */
    Result<?> getRecentContacts(String coinType);

    /**
     * 获取常用地址
     */
    Result<?> getFrequentAddresses(String coinType);

    /**
     * 验证地址有效性
     */
    Result<?> validateAddress(String address, String coinType);

    /**
     * 导出地址簿
     */
    Result<?> exportAddressBook();

    /**
     * 导入地址簿
     */
    Result<?> importAddressBook(MultipartFile file);

    /**
     * 获取地址标签
     */
    Result<?> getAddressLabels();

    /**
     * 创建地址标签
     */
    Result<?> createAddressLabel(LabelCreateDTO dto);

    /**
     * 设置常用地址
     */
    Result<?> setFrequentAddress(Long contactId, Boolean isFrequent);

    /**
     * 搜索联系人
     */
    Result<?> searchContacts(String keyword, String coinType);
}
