package com.meh.businesscard.service;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.WalletCreateDTO;
import com.meh.businesscard.dto.WalletUpdateDTO;
import com.meh.businesscard.dto.WalletRecoverDTO;

/**
 * 钱包服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface WalletService {

    /**
     * 创建钱包
     */
    Result<?> createWallet(WalletCreateDTO dto);

    /**
     * 获取钱包列表
     */
    Result<?> getWalletList();

    /**
     * 获取钱包详情
     */
    Result<?> getWalletDetail(Long walletId);

    /**
     * 更新钱包信息
     */
    Result<?> updateWallet(Long walletId, WalletUpdateDTO dto);

    /**
     * 删除钱包
     */
    Result<?> deleteWallet(Long walletId, String password);

    /**
     * 恢复钱包
     */
    Result<?> recoverWallet(WalletRecoverDTO dto);

    /**
     * 获取钱包余额
     */
    Result<?> getWalletBalance(Long walletId);

    /**
     * 刷新钱包余额
     */
    Result<?> refreshWalletBalance(Long walletId);

    /**
     * 获取资产组合统计
     */
    Result<?> getWalletPortfolio(Long walletId);

    /**
     * 设置主钱包
     */
    Result<?> setMainWallet(Long walletId);

    /**
     * 生成助记词
     */
    Result<?> generateMnemonic(Integer wordCount);

    /**
     * 验证助记词
     */
    Result<?> validateMnemonic(String mnemonic);

    /**
     * 获取支持的币种列表
     */
    Result<?> getSupportedCoins();

    /**
     * 与名片系统关联
     */
    Result<?> bindToCard(Long walletId, Long cardId);
}
