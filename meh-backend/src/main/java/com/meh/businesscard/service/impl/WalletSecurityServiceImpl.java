package com.meh.businesscard.service.impl;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.*;
import com.meh.businesscard.dto.BiometricSetupDTO;
import com.meh.businesscard.dto.BiometricVerifyDTO;
import com.meh.businesscard.entity.WalletSecurity;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.mapper.WalletSecurityMapper;
import com.meh.businesscard.service.WalletSecurityService;
import com.meh.businesscard.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 钱包安全服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WalletSecurityServiceImpl implements WalletSecurityService {

    private final WalletSecurityMapper securityMapper;
    private final UserService userService;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public Result<?> setTransactionPassword(TransactionPasswordDTO dto) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 验证确认密码
            if (!dto.getNewPassword().equals(dto.getConfirmPassword())) {
                return Result.failed("两次输入的密码不一致");
            }

            // 获取或创建安全设置
            WalletSecurity security = securityMapper.selectByUserIdAndWalletId(currentUser.getId(), dto.getWalletId());
            if (security == null) {
                security = new WalletSecurity();
                security.setUserId(currentUser.getId());
                security.setWalletId(dto.getWalletId());
                security.setCreateTime(LocalDateTime.now());
            }

            // 如果是修改密码，验证旧密码
            if (dto.getOldPassword() != null && security.getTransactionPassword() != null) {
                if (!passwordEncoder.matches(dto.getOldPassword(), security.getTransactionPassword())) {
                    return Result.failed("原密码错误");
                }
            }

            // 设置新密码
            security.setTransactionPassword(passwordEncoder.encode(dto.getNewPassword()));
            security.setBiometricEnabled(dto.getEnableBiometric());
            security.setBiometricType(dto.getBiometricType());
            security.setUpdateTime(LocalDateTime.now());

            if (security.getId() == null) {
                securityMapper.insert(security);
            } else {
                securityMapper.updateById(security);
            }

            return Result.success("交易密码设置成功");

        } catch (Exception e) {
            log.error("设置交易密码失败: {}", e.getMessage(), e);
            return Result.failed("设置交易密码失败");
        }
    }

    @Override
    public Result<?> verifyTransactionPassword(String password) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取全局安全设置
            WalletSecurity security = securityMapper.selectGlobalSecurityByUserId(currentUser.getId());
            if (security == null || security.getTransactionPassword() == null) {
                return Result.failed("未设置交易密码");
            }

            // 验证密码
            boolean isValid = passwordEncoder.matches(password, security.getTransactionPassword());
            
            if (isValid) {
                // 更新最后验证时间
                securityMapper.updateLastVerifyTime(security.getId());
            }

            Map<String, Object> result = new HashMap<>();
            result.put("isValid", isValid);
            result.put("message", isValid ? "密码验证成功" : "密码验证失败");

            return Result.success(result);

        } catch (Exception e) {
            log.error("验证交易密码失败: {}", e.getMessage(), e);
            return Result.failed("验证交易密码失败");
        }
    }

    @Override
    @Transactional
    public Result<?> enableBiometric(BiometricSetupDTO dto) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // TODO: 实现生物识别启用逻辑
            // 1. 验证生物识别数据
            // 2. 保存生物识别模板
            // 3. 更新安全设置

            return Result.success("生物识别启用成功");

        } catch (Exception e) {
            log.error("启用生物识别失败: {}", e.getMessage(), e);
            return Result.failed("启用生物识别失败");
        }
    }

    @Override
    public Result<?> disableBiometric(String password) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 验证交易密码
            Result<?> verifyResult = verifyTransactionPassword(password);
            if (!verifyResult.isSuccess()) {
                return verifyResult;
            }

            // TODO: 实现生物识别禁用逻辑

            return Result.success("生物识别已禁用");

        } catch (Exception e) {
            log.error("禁用生物识别失败: {}", e.getMessage(), e);
            return Result.failed("禁用生物识别失败");
        }
    }

    @Override
    public Result<?> verifyBiometric(BiometricVerifyDTO dto) {
        try {
            // TODO: 实现生物识别验证逻辑
            Map<String, Object> result = new HashMap<>();
            result.put("isValid", true);
            result.put("message", "生物识别验证成功");

            return Result.success(result);

        } catch (Exception e) {
            log.error("生物识别验证失败: {}", e.getMessage(), e);
            return Result.failed("生物识别验证失败");
        }
    }

    @Override
    @Transactional
    public Result<?> setupMultiSignature(MultiSigSetupDTO dto) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 验证交易密码
            Result<?> verifyResult = verifyTransactionPassword(dto.getTransactionPassword());
            if (!verifyResult.isSuccess()) {
                return verifyResult;
            }

            // 获取或创建安全设置
            WalletSecurity security = securityMapper.selectByWalletId(dto.getWalletId());
            if (security == null) {
                security = new WalletSecurity();
                security.setUserId(currentUser.getId());
                security.setWalletId(dto.getWalletId());
                security.setCreateTime(LocalDateTime.now());
            }

            // 设置多重签名配置
            security.setMultiSigEnabled(dto.getEnabled());
            security.setMultiSigThreshold(dto.getThreshold());
            security.setMultiSigParticipants(dto.getParticipants());
            security.setUpdateTime(LocalDateTime.now());

            if (security.getId() == null) {
                securityMapper.insert(security);
            } else {
                securityMapper.updateById(security);
            }

            return Result.success("多重签名设置成功");

        } catch (Exception e) {
            log.error("设置多重签名失败: {}", e.getMessage(), e);
            return Result.failed("设置多重签名失败");
        }
    }

    @Override
    public Result<?> getMultiSignatureConfig(Long walletId) {
        try {
            // 获取多重签名配置
            WalletSecurity security = securityMapper.selectByWalletId(walletId);
            
            Map<String, Object> config = new HashMap<>();
            if (security != null) {
                config.put("enabled", security.getMultiSigEnabled());
                config.put("threshold", security.getMultiSigThreshold());
                config.put("participants", security.getMultiSigParticipants());
            } else {
                config.put("enabled", false);
                config.put("threshold", 1);
                config.put("participants", 1);
            }

            return Result.success(config);

        } catch (Exception e) {
            log.error("获取多重签名配置失败: {}", e.getMessage(), e);
            return Result.failed("获取多重签名配置失败");
        }
    }

    @Override
    @Transactional
    public Result<?> setTransactionLimits(TransactionLimitsDTO dto) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 验证交易密码
            Result<?> verifyResult = verifyTransactionPassword(dto.getTransactionPassword());
            if (!verifyResult.isSuccess()) {
                return verifyResult;
            }

            // 获取或创建安全设置
            WalletSecurity security = securityMapper.selectByUserIdAndWalletId(currentUser.getId(), dto.getWalletId());
            if (security == null) {
                security = new WalletSecurity();
                security.setUserId(currentUser.getId());
                security.setWalletId(dto.getWalletId());
                security.setCreateTime(LocalDateTime.now());
            }

            // 设置交易限额
            security.setDailyLimit(dto.getDailyLimit());
            security.setMonthlyLimit(dto.getMonthlyLimit());
            security.setLargeAmountThreshold(dto.getLargeAmountThreshold());
            security.setUpdateTime(LocalDateTime.now());

            if (security.getId() == null) {
                securityMapper.insert(security);
            } else {
                securityMapper.updateById(security);
            }

            return Result.success("交易限额设置成功");

        } catch (Exception e) {
            log.error("设置交易限额失败: {}", e.getMessage(), e);
            return Result.failed("设置交易限额失败");
        }
    }

    @Override
    public Result<?> getTransactionLimits(Long walletId) {
        try {
            // 获取交易限额配置
            WalletSecurity security = securityMapper.selectByWalletId(walletId);
            
            Map<String, Object> limits = new HashMap<>();
            if (security != null) {
                limits.put("dailyLimit", security.getDailyLimit());
                limits.put("monthlyLimit", security.getMonthlyLimit());
                limits.put("largeAmountThreshold", security.getLargeAmountThreshold());
            } else {
                limits.put("dailyLimit", null);
                limits.put("monthlyLimit", null);
                limits.put("largeAmountThreshold", null);
            }

            return Result.success(limits);

        } catch (Exception e) {
            log.error("获取交易限额失败: {}", e.getMessage(), e);
            return Result.failed("获取交易限额失败");
        }
    }

    @Override
    @Transactional
    public Result<?> bindDevice(DeviceBindingDTO dto) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 验证交易密码
            Result<?> verifyResult = verifyTransactionPassword(dto.getTransactionPassword());
            if (!verifyResult.isSuccess()) {
                return verifyResult;
            }

            // 检查设备是否已绑定
            boolean isAlreadyBound = securityMapper.isDeviceBound(currentUser.getId(), dto.getDeviceFingerprint());
            if (isAlreadyBound) {
                return Result.failed("设备已绑定");
            }

            // 创建设备绑定记录
            WalletSecurity security = new WalletSecurity();
            security.setUserId(currentUser.getId());
            security.setWalletId(dto.getWalletId());
            security.setDeviceBindingEnabled(dto.getEnabled());
            security.setDeviceFingerprint(dto.getDeviceFingerprint());
            security.setCreateTime(LocalDateTime.now());
            security.setUpdateTime(LocalDateTime.now());

            securityMapper.insert(security);

            return Result.success("设备绑定成功");

        } catch (Exception e) {
            log.error("绑定设备失败: {}", e.getMessage(), e);
            return Result.failed("绑定设备失败");
        }
    }

    @Override
    public Result<?> unbindDevice(String deviceId, String password) {
        try {
            // TODO: 实现设备解绑逻辑
            return Result.success("设备解绑成功");

        } catch (Exception e) {
            log.error("解绑设备失败: {}", e.getMessage(), e);
            return Result.failed("解绑设备失败");
        }
    }

    @Override
    public Result<?> getBoundDevices() {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取绑定设备列表
            List<WalletSecurity> boundDevices = securityMapper.selectDeviceBindingEnabled(currentUser.getId());
            
            return Result.success(boundDevices);

        } catch (Exception e) {
            log.error("获取绑定设备失败: {}", e.getMessage(), e);
            return Result.failed("获取绑定设备失败");
        }
    }

    @Override
    @Transactional
    public Result<?> setCoolingPeriod(CoolingPeriodDTO dto) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 验证交易密码
            Result<?> verifyResult = verifyTransactionPassword(dto.getTransactionPassword());
            if (!verifyResult.isSuccess()) {
                return verifyResult;
            }

            // 获取或创建安全设置
            WalletSecurity security = securityMapper.selectByUserIdAndWalletId(currentUser.getId(), dto.getWalletId());
            if (security == null) {
                security = new WalletSecurity();
                security.setUserId(currentUser.getId());
                security.setWalletId(dto.getWalletId());
                security.setCreateTime(LocalDateTime.now());
            }

            // 设置冷却期
            security.setCoolingPeriodEnabled(dto.getEnabled());
            security.setCoolingPeriodHours(dto.getCoolingPeriodHours());
            security.setUpdateTime(LocalDateTime.now());

            if (security.getId() == null) {
                securityMapper.insert(security);
            } else {
                securityMapper.updateById(security);
            }

            return Result.success("冷却期设置成功");

        } catch (Exception e) {
            log.error("设置冷却期失败: {}", e.getMessage(), e);
            return Result.failed("设置冷却期失败");
        }
    }

    @Override
    public Result<?> getSecuritySettings(Long walletId) {
        try {
            // 获取安全设置
            WalletSecurity security = securityMapper.selectByWalletId(walletId);
            
            Map<String, Object> settings = new HashMap<>();
            if (security != null) {
                settings.put("hasTransactionPassword", security.getTransactionPassword() != null);
                settings.put("biometricEnabled", security.getBiometricEnabled());
                settings.put("biometricType", security.getBiometricType());
                settings.put("multiSigEnabled", security.getMultiSigEnabled());
                settings.put("deviceBindingEnabled", security.getDeviceBindingEnabled());
                settings.put("coolingPeriodEnabled", security.getCoolingPeriodEnabled());
                settings.put("dailyLimit", security.getDailyLimit());
                settings.put("monthlyLimit", security.getMonthlyLimit());
            } else {
                settings.put("hasTransactionPassword", false);
                settings.put("biometricEnabled", false);
                settings.put("multiSigEnabled", false);
                settings.put("deviceBindingEnabled", false);
                settings.put("coolingPeriodEnabled", false);
            }

            return Result.success(settings);

        } catch (Exception e) {
            log.error("获取安全设置失败: {}", e.getMessage(), e);
            return Result.failed("获取安全设置失败");
        }
    }

    @Override
    public Result<?> performSecurityCheck(Long walletId) {
        try {
            // TODO: 实现安全检查逻辑
            Map<String, Object> checkResult = new HashMap<>();
            checkResult.put("securityLevel", "HIGH");
            checkResult.put("score", 85);
            checkResult.put("suggestions", Arrays.asList(
                "建议启用生物识别验证",
                "建议设置交易限额",
                "建议定期更换交易密码"
            ));

            return Result.success(checkResult);

        } catch (Exception e) {
            log.error("安全检查失败: {}", e.getMessage(), e);
            return Result.failed("安全检查失败");
        }
    }

    @Override
    public Result<?> getSecurityLogs(Long walletId, Integer pageNum, Integer pageSize) {
        try {
            // TODO: 实现安全日志查询逻辑
            Map<String, Object> result = new HashMap<>();
            result.put("records", new ArrayList<>());
            result.put("total", 0);
            result.put("current", pageNum);
            result.put("size", pageSize);

            return Result.success(result);

        } catch (Exception e) {
            log.error("获取安全日志失败: {}", e.getMessage(), e);
            return Result.failed("获取安全日志失败");
        }
    }

    @Override
    public Result<?> resetSecuritySettings(Long walletId, String password) {
        try {
            // TODO: 实现安全设置重置逻辑
            return Result.success("安全设置已重置");

        } catch (Exception e) {
            log.error("重置安全设置失败: {}", e.getMessage(), e);
            return Result.failed("重置安全设置失败");
        }
    }

    @Override
    public Result<?> checkDeviceTrust(String deviceFingerprint) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 检查设备是否可信
            boolean isTrusted = securityMapper.isDeviceBound(currentUser.getId(), deviceFingerprint);
            
            Map<String, Object> result = new HashMap<>();
            result.put("isTrusted", isTrusted);
            result.put("deviceFingerprint", deviceFingerprint);

            return Result.success(result);

        } catch (Exception e) {
            log.error("检查设备信任状态失败: {}", e.getMessage(), e);
            return Result.failed("检查设备信任状态失败");
        }
    }

    @Override
    @Transactional
    public Result<?> emergencyFreeze(Long walletId, String reason) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // TODO: 实现紧急冻结逻辑
            // 1. 冻结钱包
            // 2. 记录冻结原因
            // 3. 发送通知

            return Result.success("钱包已紧急冻结");

        } catch (Exception e) {
            log.error("紧急冻结钱包失败: {}", e.getMessage(), e);
            return Result.failed("紧急冻结钱包失败");
        }
    }

    @Override
    @Transactional
    public Result<?> unfreezeWallet(Long walletId, String password) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 验证交易密码
            Result<?> verifyResult = verifyTransactionPassword(password);
            if (!verifyResult.isSuccess()) {
                return verifyResult;
            }

            // TODO: 实现解冻逻辑
            // 1. 解冻钱包
            // 2. 记录解冻操作
            // 3. 发送通知

            return Result.success("钱包已解冻");

        } catch (Exception e) {
            log.error("解冻钱包失败: {}", e.getMessage(), e);
            return Result.failed("解冻钱包失败");
        }
    }
}
