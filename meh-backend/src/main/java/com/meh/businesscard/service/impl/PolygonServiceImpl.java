package com.meh.businesscard.service.impl;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.config.PolygonConfig;
import com.meh.businesscard.contract.ERC20Contract;
import com.meh.businesscard.service.PolygonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.web3j.crypto.*;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.response.*;
import org.web3j.protocol.http.HttpService;
import org.web3j.utils.Convert;
import org.web3j.utils.Numeric;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Polygon区块链服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PolygonServiceImpl implements PolygonService {

    private final PolygonConfig polygonConfig;
    private Web3j web3j;

    @PostConstruct
    public void init() {
        try {
            // 初始化Web3j连接到Polygon网络
            String rpcUrl = polygonConfig.getCurrentNetwork().getRpcUrl();
            this.web3j = Web3j.build(new HttpService(rpcUrl));
            
            log.info("成功连接到Polygon网络: {}", polygonConfig.getCurrentNetwork().getNetworkName());
            log.info("RPC URL: {}", rpcUrl);
            log.info("Chain ID: {}", polygonConfig.getCurrentNetwork().getChainId());
            
        } catch (Exception e) {
            log.error("连接Polygon网络失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public Result<?> generateWalletAddress(String mnemonic, String password) {
        try {
            // 从助记词生成种子
            byte[] seed = MnemonicUtils.generateSeed(mnemonic, password != null ? password : "");
            
            // 生成主私钥
            ECKeyPair masterKeyPair = ECKeyPair.create(Hash.sha256(seed));
            
            // 生成钱包地址
            String address = "0x" + Keys.getAddress(masterKeyPair);
            String privateKey = masterKeyPair.getPrivateKey().toString(16);
            
            Map<String, Object> result = new HashMap<>();
            result.put("address", address);
            result.put("privateKey", privateKey);
            result.put("network", polygonConfig.getCurrentNetwork().getNetworkName());
            result.put("chainId", polygonConfig.getCurrentNetwork().getChainId());
            
            log.info("成功生成Polygon钱包地址: {}", address);
            return Result.success(result, "钱包地址生成成功");
            
        } catch (Exception e) {
            log.error("生成Polygon钱包地址失败: {}", e.getMessage(), e);
            return Result.failed("生成钱包地址失败: " + e.getMessage());
        }
    }

    @Override
    public Result<BigDecimal> getMaticBalance(String address) {
        try {
            if (!isValidAddress(address)) {
                return Result.failed("无效的钱包地址");
            }
            
            EthGetBalance ethGetBalance = web3j.ethGetBalance(address, DefaultBlockParameterName.LATEST).send();
            BigInteger balanceWei = ethGetBalance.getBalance();
            
            // 将Wei转换为MATIC (1 MATIC = 10^18 Wei)
            BigDecimal balance = Convert.fromWei(balanceWei.toString(), Convert.Unit.ETHER);
            
            log.info("获取MATIC余额成功 - 地址: {}, 余额: {} MATIC", address, balance);
            return Result.success(balance);
            
        } catch (Exception e) {
            log.error("获取MATIC余额失败 - 地址: {}, 错误: {}", address, e.getMessage(), e);
            return Result.failed("获取余额失败: " + e.getMessage());
        }
    }

    @Override
    public Result<BigDecimal> getTokenBalance(String address, String contractAddress) {
        try {
            if (!isValidAddress(address) || !isValidAddress(contractAddress)) {
                return Result.failed("无效的地址");
            }

            log.info("获取Token余额 - 地址: {}, 合约: {}", address, contractAddress);

            // 创建ERC-20合约实例
            ERC20Contract erc20Contract = new ERC20Contract(web3j, contractAddress);

            // 检查合约是否有效
            CompletableFuture<Boolean> contractValidFuture = erc20Contract.isContractValid();
            Boolean isValid = contractValidFuture.get();

            if (!isValid) {
                return Result.failed("无效的合约地址");
            }

            // 获取Token余额
            CompletableFuture<BigInteger> balanceFuture = erc20Contract.balanceOf(address);
            BigInteger balanceWei = balanceFuture.get();

            // 获取Token精度
            CompletableFuture<BigInteger> decimalsFuture = erc20Contract.decimals();
            BigInteger decimals = decimalsFuture.get();

            // 将余额从最小单位转换为Token单位
            BigDecimal balance = new BigDecimal(balanceWei)
                    .divide(new BigDecimal(BigInteger.TEN.pow(decimals.intValue())));

            log.info("获取Token余额成功 - 地址: {}, 合约: {}, 余额: {}", address, contractAddress, balance);
            return Result.success(balance);

        } catch (Exception e) {
            log.error("获取Token余额失败 - 地址: {}, 合约: {}, 错误: {}", address, contractAddress, e.getMessage(), e);
            return Result.failed("获取Token余额失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> sendMatic(String fromAddress, String toAddress, BigDecimal amount, String privateKey) {
        try {
            if (!isValidAddress(fromAddress) || !isValidAddress(toAddress)) {
                return Result.failed("无效的地址");
            }
            
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.failed("转账金额必须大于0");
            }
            
            // 创建凭证
            Credentials credentials = Credentials.create(privateKey);
            
            // 获取nonce
            EthGetTransactionCount ethGetTransactionCount = web3j.ethGetTransactionCount(
                fromAddress, DefaultBlockParameterName.LATEST).send();
            BigInteger nonce = ethGetTransactionCount.getTransactionCount();
            
            // 获取Gas价格
            EthGasPrice ethGasPrice = web3j.ethGasPrice().send();
            BigInteger gasPrice = ethGasPrice.getGasPrice();
            
            // 转换金额为Wei
            BigInteger value = Convert.toWei(amount, Convert.Unit.ETHER).toBigInteger();
            
            // 创建交易
            RawTransaction rawTransaction = RawTransaction.createEtherTransaction(
                nonce, gasPrice, BigInteger.valueOf(polygonConfig.getGas().getDefaultGasLimit()), toAddress, value);
            
            // 签名交易
            byte[] signedMessage = TransactionEncoder.signMessage(rawTransaction, 
                polygonConfig.getCurrentNetwork().getChainId(), credentials);
            String hexValue = Numeric.toHexString(signedMessage);
            
            // 发送交易
            EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(hexValue).send();
            String txHash = ethSendTransaction.getTransactionHash();
            
            if (ethSendTransaction.hasError()) {
                return Result.failed("交易失败: " + ethSendTransaction.getError().getMessage());
            }
            
            log.info("MATIC转账成功 - 交易哈希: {}", txHash);
            return Result.success(txHash, "转账成功");
            
        } catch (Exception e) {
            log.error("MATIC转账失败: {}", e.getMessage(), e);
            return Result.failed("转账失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> sendToken(String fromAddress, String toAddress, BigDecimal amount,
                                  String contractAddress, String privateKey) {
        try {
            if (!isValidAddress(fromAddress) || !isValidAddress(toAddress) || !isValidAddress(contractAddress)) {
                return Result.failed("无效的地址");
            }

            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return Result.failed("转账金额必须大于0");
            }

            log.info("Token转账 - 从: {}, 到: {}, 数量: {}, 合约: {}",
                fromAddress, toAddress, amount, contractAddress);

            // 创建凭证
            Credentials credentials = Credentials.create(privateKey);

            // 创建ERC-20合约实例
            ERC20Contract erc20Contract = new ERC20Contract(web3j, contractAddress);

            // 获取Token精度
            CompletableFuture<BigInteger> decimalsFuture = erc20Contract.decimals();
            BigInteger decimals = decimalsFuture.get();

            // 将金额转换为最小单位
            BigInteger value = amount.multiply(new BigDecimal(BigInteger.TEN.pow(decimals.intValue()))).toBigInteger();

            // 获取nonce
            EthGetTransactionCount ethGetTransactionCount = web3j.ethGetTransactionCount(
                fromAddress, DefaultBlockParameterName.LATEST).send();
            BigInteger nonce = ethGetTransactionCount.getTransactionCount();

            // 获取Gas价格
            EthGasPrice ethGasPrice = web3j.ethGasPrice().send();
            BigInteger gasPrice = ethGasPrice.getGasPrice();

            // 创建转账交易数据
            String data = erc20Contract.createTransferData(toAddress, value);

            // 估算Gas限制
            CompletableFuture<BigInteger> gasLimitFuture = erc20Contract.estimateGas(fromAddress, data);
            BigInteger gasLimit = gasLimitFuture.get();

            // 创建交易
            RawTransaction rawTransaction = RawTransaction.createTransaction(
                nonce, gasPrice, gasLimit, contractAddress, BigInteger.ZERO, data);

            // 签名交易
            byte[] signedMessage = TransactionEncoder.signMessage(rawTransaction,
                polygonConfig.getCurrentNetwork().getChainId(), credentials);
            String hexValue = Numeric.toHexString(signedMessage);

            // 发送交易
            EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(hexValue).send();
            String txHash = ethSendTransaction.getTransactionHash();

            if (ethSendTransaction.hasError()) {
                return Result.failed("Token转账失败: " + ethSendTransaction.getError().getMessage());
            }

            log.info("Token转账成功 - 交易哈希: {}", txHash);
            return Result.success(txHash, "Token转账成功");

        } catch (Exception e) {
            log.error("Token转账失败: {}", e.getMessage(), e);
            return Result.failed("Token转账失败: " + e.getMessage());
        }
    }

    @Override
    public Result<?> estimateGasFee(String fromAddress, String toAddress, BigDecimal amount, String contractAddress) {
        try {
            // 获取当前Gas价格
            EthGasPrice ethGasPrice = web3j.ethGasPrice().send();
            BigInteger gasPrice = ethGasPrice.getGasPrice();
            
            // 估算Gas限制
            BigInteger gasLimit;
            if (contractAddress != null && !contractAddress.isEmpty()) {
                // Token转账需要更多Gas
                gasLimit = BigInteger.valueOf(polygonConfig.getGas().getContractGasLimit());
            } else {
                // 普通MATIC转账
                gasLimit = BigInteger.valueOf(polygonConfig.getGas().getDefaultGasLimit());
            }
            
            // 计算总Gas费用
            BigInteger totalGasFee = gasPrice.multiply(gasLimit);
            BigDecimal gasFeeInMatic = Convert.fromWei(totalGasFee.toString(), Convert.Unit.ETHER);
            
            Map<String, Object> result = new HashMap<>();
            result.put("gasPrice", gasPrice);
            result.put("gasLimit", gasLimit);
            result.put("totalGasFee", totalGasFee);
            result.put("gasFeeInMatic", gasFeeInMatic);
            result.put("currency", "MATIC");
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("估算Gas费用失败: {}", e.getMessage(), e);
            return Result.failed("估算Gas费用失败: " + e.getMessage());
        }
    }

    @Override
    public Result<?> getTransactionDetails(String txHash) {
        try {
            EthTransaction ethTransaction = web3j.ethGetTransactionByHash(txHash).send();
            
            if (!ethTransaction.getTransaction().isPresent()) {
                return Result.failed("交易不存在");
            }
            
            Transaction transaction = ethTransaction.getTransaction().get();
            
            // 获取交易收据
            EthGetTransactionReceipt receipt = web3j.ethGetTransactionReceipt(txHash).send();
            
            Map<String, Object> result = new HashMap<>();
            result.put("hash", transaction.getHash());
            result.put("from", transaction.getFrom());
            result.put("to", transaction.getTo());
            result.put("value", Convert.fromWei(transaction.getValue().toString(), Convert.Unit.ETHER));
            result.put("gasPrice", transaction.getGasPrice());
            result.put("gasLimit", transaction.getGas());
            result.put("nonce", transaction.getNonce());
            result.put("blockNumber", transaction.getBlockNumber());
            
            if (receipt.getTransactionReceipt().isPresent()) {
                TransactionReceipt txReceipt = receipt.getTransactionReceipt().get();
                result.put("status", txReceipt.getStatus());
                result.put("gasUsed", txReceipt.getGasUsed());
                result.put("blockHash", txReceipt.getBlockHash());
            }
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取交易详情失败: {}", e.getMessage(), e);
            return Result.failed("获取交易详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<?> getTransactionHistory(String address, int page, int size) {
        try {
            // TODO: 实现交易历史查询
            // 这里需要调用区块链浏览器API或者自建索引服务
            log.info("获取交易历史 - 地址: {}, 页码: {}, 大小: {}", address, page, size);
            
            // 暂时返回空列表
            Map<String, Object> result = new HashMap<>();
            result.put("transactions", new java.util.ArrayList<>());
            result.put("total", 0);
            result.put("page", page);
            result.put("size", size);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取交易历史失败: {}", e.getMessage(), e);
            return Result.failed("获取交易历史失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isValidAddress(String address) {
        try {
            return WalletUtils.isValidAddress(address);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public Result<BigInteger> getCurrentGasPrice() {
        try {
            EthGasPrice ethGasPrice = web3j.ethGasPrice().send();
            BigInteger gasPrice = ethGasPrice.getGasPrice();
            
            return Result.success(gasPrice);
            
        } catch (Exception e) {
            log.error("获取Gas价格失败: {}", e.getMessage(), e);
            return Result.failed("获取Gas价格失败: " + e.getMessage());
        }
    }

    @Override
    public Result<?> getNetworkStatus() {
        try {
            EthBlockNumber ethBlockNumber = web3j.ethBlockNumber().send();
            BigInteger blockNumber = ethBlockNumber.getBlockNumber();
            
            EthGasPrice ethGasPrice = web3j.ethGasPrice().send();
            BigInteger gasPrice = ethGasPrice.getGasPrice();
            
            Map<String, Object> result = new HashMap<>();
            result.put("networkName", polygonConfig.getCurrentNetwork().getNetworkName());
            result.put("chainId", polygonConfig.getCurrentNetwork().getChainId());
            result.put("latestBlock", blockNumber);
            result.put("gasPrice", gasPrice);
            result.put("currency", polygonConfig.getCurrentNetwork().getCurrencySymbol());
            result.put("blockExplorer", polygonConfig.getCurrentNetwork().getBlockExplorer());
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取网络状态失败: {}", e.getMessage(), e);
            return Result.failed("获取网络状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> deployMehToken(String deployerPrivateKey) {
        try {
            // TODO: 实现MEH Token合约部署
            log.info("部署MEH Token合约");
            
            // 暂时返回模拟合约地址
            String contractAddress = "0x" + System.currentTimeMillis();
            return Result.success(contractAddress, "合约部署成功");
            
        } catch (Exception e) {
            log.error("部署MEH Token合约失败: {}", e.getMessage(), e);
            return Result.failed("合约部署失败: " + e.getMessage());
        }
    }

    @Override
    public Result<String> mintMehToken(String toAddress, BigDecimal amount, String ownerPrivateKey) {
        try {
            // TODO: 实现MEH Token铸造
            log.info("铸造MEH Token - 地址: {}, 数量: {}", toAddress, amount);
            
            // 暂时返回模拟交易哈希
            String txHash = "0x" + System.currentTimeMillis();
            return Result.success(txHash, "Token铸造成功");
            
        } catch (Exception e) {
            log.error("铸造MEH Token失败: {}", e.getMessage(), e);
            return Result.failed("Token铸造失败: " + e.getMessage());
        }
    }

    @Override
    public Result<?> getMehTokenInfo() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("name", polygonConfig.getMehToken().getName());
            result.put("symbol", polygonConfig.getMehToken().getSymbol());
            result.put("decimals", polygonConfig.getMehToken().getDecimals());
            result.put("totalSupply", polygonConfig.getMehToken().getInitialSupply());
            result.put("contractAddress", polygonConfig.getMehToken().getContractAddress());
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("获取MEH Token信息失败: {}", e.getMessage(), e);
            return Result.failed("获取Token信息失败: " + e.getMessage());
        }
    }
}
