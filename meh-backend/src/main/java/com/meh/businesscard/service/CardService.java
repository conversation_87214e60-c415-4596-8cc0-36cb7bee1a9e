package com.meh.businesscard.service;

import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.CardCreateDTO;
import com.meh.businesscard.dto.CardUpdateDTO;
import com.meh.businesscard.entity.Card;
import com.meh.businesscard.entity.CardComment;

import java.util.List;
import java.util.Map;

/**
 * 名片服务接口
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
public interface CardService {

    /**
     * 创建名片
     *
     * @param cardCreateDTO 名片创建参数
     * @return 创建结果
     */
    Result<?> createCard(CardCreateDTO cardCreateDTO);

    /**
     * 更新名片
     *
     * @param cardUpdateDTO 名片更新参数
     * @return 更新结果
     */
    Result<?> updateCard(CardUpdateDTO cardUpdateDTO);

    /**
     * 删除名片
     *
     * @param id 名片ID
     * @return 删除结果
     */
    Result<?> deleteCard(Long id);

    /**
     * 获取名片详情
     *
     * @param id 名片ID
     * @return 名片详情
     */
    Result<Card> getCardDetail(Long id);

    /**
     * 获取我的名片列表
     *
     * @return 名片列表
     */
    Result<List<Card>> getMyCardList();

    /**
     * 设置默认名片
     *
     * @param id 名片ID
     * @return 设置结果
     */
    Result<?> setDefaultCard(Long id);

    /**
     * 获取名片模板列表
     *
     * @return 模板列表
     */
    Result<?> getTemplateList();

    /**
     * 记录名片访问
     *
     * @param id 名片ID
     * @return 记录结果
     */
    Result<?> visitCard(Long id);

    /**
     * 点赞名片
     *
     * @param id 名片ID
     * @return 点赞结果
     */
    Result<?> likeCard(Long id);

    /**
     * 取消点赞名片
     *
     * @param id 名片ID
     * @return 取消点赞结果
     */
    Result<?> unlikeCard(Long id);

    /**
     * 评论名片
     *
     * @param id 名片ID
     * @param content 评论内容
     * @return 评论结果
     */
    Result<?> commentCard(Long id, String content);

    /**
     * 获取名片访问记录
     *
     * @param id 名片ID
     * @return 访问记录
     */
    Result<?> getVisitLog(Long id);

    /**
     * 获取名片分享链接
     *
     * @param id 名片ID
     * @return 分享链接
     */
    Result<String> getShareLink(Long id);

    /**
     * 获取名片评论列表
     *
     * @param id 名片ID
     * @return 评论列表
     */
    Result<List<CardComment>> getCardComments(Long id);

    /**
     * 检查点赞状态
     *
     * @param id 名片ID
     * @return 点赞状态
     */
    Result<?> checkLikeStatus(Long id);

    /**
     * 获取推荐名片列表
     *
     * @return 推荐名片列表
     */
    Result<?> getRecommendCards();

    // =============================================
    // 钱包系统集成方法
    // =============================================

    /**
     * 添加钱包到名片
     *
     * @param cardId 名片ID
     * @param request 钱包信息
     * @return 添加结果
     */
    Result<?> addWalletToCard(Long cardId, Map<String, Object> request);

    /**
     * 获取名片关联的钱包
     *
     * @param cardId 名片ID
     * @return 钱包列表
     */
    Result<?> getCardWallets(Long cardId);

    /**
     * 移除名片钱包
     *
     * @param cardId 名片ID
     * @param walletId 钱包ID
     * @return 移除结果
     */
    Result<?> removeWalletFromCard(Long cardId, Long walletId);

    /**
     * 设置名片主钱包
     *
     * @param cardId 名片ID
     * @param walletId 钱包ID
     * @return 设置结果
     */
    Result<?> setCardMainWallet(Long cardId, Long walletId);

    /**
     * 获取名片收款二维码
     *
     * @param cardId 名片ID
     * @param coinType 币种类型
     * @return 二维码信息
     */
    Result<?> getCardPaymentQR(Long cardId, String coinType);
}
