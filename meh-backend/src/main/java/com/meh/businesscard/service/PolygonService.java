package com.meh.businesscard.service;

import com.meh.businesscard.common.api.Result;
import org.web3j.protocol.core.methods.response.TransactionReceipt;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * Polygon区块链服务接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface PolygonService {

    /**
     * 生成Polygon钱包地址
     * 
     * @param mnemonic 助记词
     * @param password 密码
     * @return 钱包地址和私钥信息
     */
    Result<?> generateWalletAddress(String mnemonic, String password);

    /**
     * 获取MATIC余额
     * 
     * @param address 钱包地址
     * @return MATIC余额
     */
    Result<BigDecimal> getMaticBalance(String address);

    /**
     * 获取Token余额
     * 
     * @param address 钱包地址
     * @param contractAddress Token合约地址
     * @return Token余额
     */
    Result<BigDecimal> getTokenBalance(String address, String contractAddress);

    /**
     * 发送MATIC转账
     * 
     * @param fromAddress 发送方地址
     * @param toAddress 接收方地址
     * @param amount 转账金额
     * @param privateKey 私钥
     * @return 交易哈希
     */
    Result<String> sendMatic(String fromAddress, String toAddress, BigDecimal amount, String privateKey);

    /**
     * 发送Token转账
     * 
     * @param fromAddress 发送方地址
     * @param toAddress 接收方地址
     * @param amount 转账金额
     * @param contractAddress Token合约地址
     * @param privateKey 私钥
     * @return 交易哈希
     */
    Result<String> sendToken(String fromAddress, String toAddress, BigDecimal amount, 
                           String contractAddress, String privateKey);

    /**
     * 估算Gas费用
     * 
     * @param fromAddress 发送方地址
     * @param toAddress 接收方地址
     * @param amount 转账金额
     * @param contractAddress Token合约地址（可选）
     * @return Gas费用估算
     */
    Result<?> estimateGasFee(String fromAddress, String toAddress, BigDecimal amount, String contractAddress);

    /**
     * 获取交易详情
     * 
     * @param txHash 交易哈希
     * @return 交易详情
     */
    Result<?> getTransactionDetails(String txHash);

    /**
     * 获取交易历史
     * 
     * @param address 钱包地址
     * @param page 页码
     * @param size 每页大小
     * @return 交易历史列表
     */
    Result<?> getTransactionHistory(String address, int page, int size);

    /**
     * 验证地址格式
     * 
     * @param address 钱包地址
     * @return 是否有效
     */
    boolean isValidAddress(String address);

    /**
     * 获取当前Gas价格
     * 
     * @return Gas价格 (Gwei)
     */
    Result<BigInteger> getCurrentGasPrice();

    /**
     * 获取网络状态
     * 
     * @return 网络状态信息
     */
    Result<?> getNetworkStatus();

    /**
     * 部署MEH Token合约
     * 
     * @param deployerPrivateKey 部署者私钥
     * @return 合约地址
     */
    Result<String> deployMehToken(String deployerPrivateKey);

    /**
     * 铸造MEH Token
     * 
     * @param toAddress 接收地址
     * @param amount 铸造数量
     * @param ownerPrivateKey 合约所有者私钥
     * @return 交易哈希
     */
    Result<String> mintMehToken(String toAddress, BigDecimal amount, String ownerPrivateKey);

    /**
     * 获取MEH Token信息
     * 
     * @return Token信息
     */
    Result<?> getMehTokenInfo();
}
