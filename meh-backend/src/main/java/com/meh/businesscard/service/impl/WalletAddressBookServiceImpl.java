package com.meh.businesscard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meh.businesscard.common.api.Result;
import com.meh.businesscard.dto.AddressBookCreateDTO;
import com.meh.businesscard.dto.AddressBookUpdateDTO;
import com.meh.businesscard.dto.LabelCreateDTO;
import com.meh.businesscard.dto.BatchDeleteDTO;
import com.meh.businesscard.dto.CardImportDTO;
import com.meh.businesscard.entity.WalletAddressBook;
import com.meh.businesscard.entity.User;
import com.meh.businesscard.mapper.WalletAddressBookMapper;
import com.meh.businesscard.service.WalletAddressBookService;
import com.meh.businesscard.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 钱包地址簿服务实现类
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WalletAddressBookServiceImpl implements WalletAddressBookService {

    private final WalletAddressBookMapper addressBookMapper;
    private final UserService userService;

    @Override
    @Transactional
    public Result<?> addContact(AddressBookCreateDTO dto) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 检查地址是否已存在
            WalletAddressBook existing = addressBookMapper.selectByAddressAndCoinType(
                currentUser.getId(), dto.getWalletAddress(), dto.getCoinType());
            if (existing != null) {
                return Result.failed("该地址已存在于地址簿中");
            }

            // 创建地址簿记录
            WalletAddressBook addressBook = new WalletAddressBook();
            BeanUtils.copyProperties(dto, addressBook);
            addressBook.setUserId(currentUser.getId());
            addressBook.setUseCount(0);
            addressBook.setDeleted(false);
            addressBook.setCreateTime(LocalDateTime.now());
            addressBook.setUpdateTime(LocalDateTime.now());

            addressBookMapper.insert(addressBook);

            return Result.success("联系人添加成功");

        } catch (Exception e) {
            log.error("添加联系人失败: {}", e.getMessage(), e);
            return Result.failed("添加联系人失败");
        }
    }

    @Override
    public Result<?> getAddressBook(String coinType, String category, String keyword, 
                                   Integer pageNum, Integer pageSize) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 分页查询
            Page<WalletAddressBook> page = new Page<>(pageNum, pageSize);
            IPage<WalletAddressBook> addressBookPage = addressBookMapper.selectAddressBookPage(
                page, currentUser.getId(), coinType, category, keyword);

            // 构建返回数据
            Map<String, Object> result = new HashMap<>();
            result.put("records", addressBookPage.getRecords());
            result.put("total", addressBookPage.getTotal());
            result.put("current", addressBookPage.getCurrent());
            result.put("size", addressBookPage.getSize());
            result.put("pages", addressBookPage.getPages());

            return Result.success(result);

        } catch (Exception e) {
            log.error("获取地址簿失败: {}", e.getMessage(), e);
            return Result.failed("获取地址簿失败");
        }
    }

    @Override
    @Transactional
    public Result<?> updateContact(Long contactId, AddressBookUpdateDTO dto) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取联系人
            WalletAddressBook addressBook = addressBookMapper.selectById(contactId);
            if (addressBook == null) {
                return Result.failed("联系人不存在");
            }

            // 验证所有权
            if (!addressBook.getUserId().equals(currentUser.getId())) {
                return Result.failed("无权修改该联系人");
            }

            // 更新联系人信息
            BeanUtils.copyProperties(dto, addressBook);
            addressBook.setUpdateTime(LocalDateTime.now());

            addressBookMapper.updateById(addressBook);

            return Result.success("联系人更新成功");

        } catch (Exception e) {
            log.error("更新联系人失败: {}", e.getMessage(), e);
            return Result.failed("更新联系人失败");
        }
    }

    @Override
    @Transactional
    public Result<?> deleteContact(Long contactId) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取联系人
            WalletAddressBook addressBook = addressBookMapper.selectById(contactId);
            if (addressBook == null) {
                return Result.failed("联系人不存在");
            }

            // 验证所有权
            if (!addressBook.getUserId().equals(currentUser.getId())) {
                return Result.failed("无权删除该联系人");
            }

            // 软删除
            addressBook.setDeleted(true);
            addressBook.setUpdateTime(LocalDateTime.now());
            addressBookMapper.updateById(addressBook);

            return Result.success("联系人删除成功");

        } catch (Exception e) {
            log.error("删除联系人失败: {}", e.getMessage(), e);
            return Result.failed("删除联系人失败");
        }
    }

    @Override
    public Result<?> getContactDetail(Long contactId) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取联系人详情
            WalletAddressBook addressBook = addressBookMapper.selectById(contactId);
            if (addressBook == null) {
                return Result.failed("联系人不存在");
            }

            // 验证所有权
            if (!addressBook.getUserId().equals(currentUser.getId())) {
                return Result.failed("无权访问该联系人");
            }

            return Result.success(addressBook);

        } catch (Exception e) {
            log.error("获取联系人详情失败: {}", e.getMessage(), e);
            return Result.failed("获取联系人详情失败");
        }
    }

    @Override
    public Result<?> getRecentContacts(String coinType) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取最近联系人
            List<WalletAddressBook> recentContacts = addressBookMapper.selectRecentAddresses(
                currentUser.getId(), coinType, 10);

            return Result.success(recentContacts);

        } catch (Exception e) {
            log.error("获取最近联系人失败: {}", e.getMessage(), e);
            return Result.failed("获取最近联系人失败");
        }
    }

    @Override
    public Result<?> getFrequentAddresses(String coinType) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取常用地址
            List<WalletAddressBook> frequentAddresses = addressBookMapper.selectFrequentAddresses(
                currentUser.getId(), coinType);

            return Result.success(frequentAddresses);

        } catch (Exception e) {
            log.error("获取常用地址失败: {}", e.getMessage(), e);
            return Result.failed("获取常用地址失败");
        }
    }

    @Override
    public Result<?> validateAddress(String address, String coinType) {
        try {
            // TODO: 实现地址验证逻辑
            boolean isValid = address != null && address.length() > 20;
            String riskLevel = "LOW";
            
            Map<String, Object> result = new HashMap<>();
            result.put("isValid", isValid);
            result.put("riskLevel", riskLevel);
            result.put("message", isValid ? "地址有效" : "地址无效");
            
            return Result.success(result);

        } catch (Exception e) {
            log.error("验证地址失败: {}", e.getMessage(), e);
            return Result.failed("验证地址失败");
        }
    }

    @Override
    public Result<?> exportAddressBook() {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取用户所有地址簿数据
            List<WalletAddressBook> addressBooks = addressBookMapper.selectList(
                new LambdaQueryWrapper<WalletAddressBook>()
                    .eq(WalletAddressBook::getUserId, currentUser.getId())
                    .eq(WalletAddressBook::getDeleted, false)
                    .orderByDesc(WalletAddressBook::getCreateTime)
            );

            // 生成CSV内容
            StringBuilder csvContent = new StringBuilder();
            csvContent.append("联系人名称,钱包地址,币种类型,地址标签,地址分类,备注,创建时间\n");

            for (WalletAddressBook addressBook : addressBooks) {
                csvContent.append(String.format("%s,%s,%s,%s,%s,%s,%s\n",
                    addressBook.getContactName(),
                    addressBook.getWalletAddress(),
                    addressBook.getCoinType(),
                    addressBook.getAddressLabel() != null ? addressBook.getAddressLabel() : "",
                    addressBook.getAddressCategory(),
                    addressBook.getRemark() != null ? addressBook.getRemark() : "",
                    addressBook.getCreateTime()
                ));
            }

            // 生成文件名
            String fileName = "addressbook_" + currentUser.getId() + "_" + System.currentTimeMillis() + ".csv";

            Map<String, Object> result = new HashMap<>();
            result.put("csvContent", csvContent.toString());
            result.put("fileName", fileName);
            result.put("recordCount", addressBooks.size());

            return Result.success(result, "地址簿导出成功");

        } catch (Exception e) {
            log.error("导出地址簿失败: {}", e.getMessage(), e);
            return Result.failed("导出地址簿失败");
        }
    }

    @Override
    @Transactional
    public Result<?> importAddressBook(MultipartFile file) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 验证文件
            if (file == null || file.isEmpty()) {
                return Result.failed("请选择要导入的文件");
            }

            String fileName = file.getOriginalFilename();
            if (fileName == null || !fileName.toLowerCase().endsWith(".csv")) {
                return Result.failed("只支持CSV格式文件");
            }

            // 读取文件内容
            String content = new String(file.getBytes(), "UTF-8");
            String[] lines = content.split("\n");

            if (lines.length <= 1) {
                return Result.failed("文件内容为空或格式错误");
            }

            // 解析CSV内容（跳过标题行）
            int successCount = 0;
            int failCount = 0;
            List<String> errors = new ArrayList<>();

            for (int i = 1; i < lines.length; i++) {
                String line = lines[i].trim();
                if (line.isEmpty()) continue;

                try {
                    String[] fields = line.split(",");
                    if (fields.length < 3) {
                        errors.add("第" + (i + 1) + "行：字段不足");
                        failCount++;
                        continue;
                    }

                    // 检查地址是否已存在
                    String contactName = fields[0].trim();
                    String walletAddress = fields[1].trim();
                    String coinType = fields[2].trim();

                    WalletAddressBook existing = addressBookMapper.selectByAddressAndCoinType(
                        currentUser.getId(), walletAddress, coinType);

                    if (existing != null) {
                        errors.add("第" + (i + 1) + "行：地址已存在");
                        failCount++;
                        continue;
                    }

                    // 创建地址簿记录
                    WalletAddressBook addressBook = new WalletAddressBook();
                    addressBook.setUserId(currentUser.getId());
                    addressBook.setContactName(contactName);
                    addressBook.setWalletAddress(walletAddress);
                    addressBook.setCoinType(coinType);
                    addressBook.setAddressLabel(fields.length > 3 ? fields[3].trim() : null);
                    addressBook.setAddressCategory(fields.length > 4 ? fields[4].trim() : "PERSONAL");
                    addressBook.setRemark(fields.length > 5 ? fields[5].trim() : null);
                    addressBook.setUseCount(0);
                    addressBook.setIsFrequent(false);
                    addressBook.setRiskLevel("LOW");
                    addressBook.setDeleted(false);
                    addressBook.setCreateTime(LocalDateTime.now());
                    addressBook.setUpdateTime(LocalDateTime.now());

                    addressBookMapper.insert(addressBook);
                    successCount++;

                } catch (Exception e) {
                    errors.add("第" + (i + 1) + "行：" + e.getMessage());
                    failCount++;
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errors", errors);

            return Result.success(result, String.format("导入完成：成功%d条，失败%d条", successCount, failCount));

        } catch (Exception e) {
            log.error("导入地址簿失败: {}", e.getMessage(), e);
            return Result.failed("导入地址簿失败：" + e.getMessage());
        }
    }

    @Override
    public Result<?> getAddressLabels() {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取所有标签
            List<String> labels = addressBookMapper.selectAllLabels(currentUser.getId());
            
            // 添加默认标签
            Set<String> allLabels = new HashSet<>(labels);
            allLabels.addAll(Arrays.asList("朋友", "同事", "客户", "交易所", "DeFi", "NFT"));
            
            return Result.success(new ArrayList<>(allLabels));

        } catch (Exception e) {
            log.error("获取地址标签失败: {}", e.getMessage(), e);
            return Result.failed("获取地址标签失败");
        }
    }

    @Override
    public Result<?> createAddressLabel(LabelCreateDTO dto) {
        try {
            // TODO: 实现标签创建逻辑（可以存储到单独的标签表）
            return Result.success("标签创建成功");

        } catch (Exception e) {
            log.error("创建地址标签失败: {}", e.getMessage(), e);
            return Result.failed("创建地址标签失败");
        }
    }

    @Override
    @Transactional
    public Result<?> setFrequentAddress(Long contactId, Boolean isFrequent) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取联系人
            WalletAddressBook addressBook = addressBookMapper.selectById(contactId);
            if (addressBook == null) {
                return Result.failed("联系人不存在");
            }

            // 验证所有权
            if (!addressBook.getUserId().equals(currentUser.getId())) {
                return Result.failed("无权修改该联系人");
            }

            // 更新常用状态
            addressBook.setIsFrequent(isFrequent);
            addressBook.setUpdateTime(LocalDateTime.now());
            addressBookMapper.updateById(addressBook);

            return Result.success(isFrequent ? "已设为常用地址" : "已取消常用地址");

        } catch (Exception e) {
            log.error("设置常用地址失败: {}", e.getMessage(), e);
            return Result.failed("设置常用地址失败");
        }
    }

    @Override
    public Result<?> searchContacts(String keyword, String coinType) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 分页查询（使用默认分页参数）
            Page<WalletAddressBook> page = new Page<>(1, 20);
            IPage<WalletAddressBook> searchResult = addressBookMapper.selectAddressBookPage(
                page, currentUser.getId(), coinType, null, keyword);

            return Result.success(searchResult.getRecords());

        } catch (Exception e) {
            log.error("搜索联系人失败: {}", e.getMessage(), e);
            return Result.failed("搜索联系人失败");
        }
    }

    @Override
    @Transactional
    public Result<?> batchDeleteContacts(BatchDeleteDTO dto) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 验证删除ID列表
            if (dto.getIds() == null || dto.getIds().isEmpty()) {
                return Result.failed("删除ID列表不能为空");
            }

            int successCount = 0;
            int failCount = 0;
            List<String> errors = new ArrayList<>();

            for (Long contactId : dto.getIds()) {
                try {
                    // 获取联系人
                    WalletAddressBook addressBook = addressBookMapper.selectById(contactId);
                    if (addressBook == null) {
                        errors.add("联系人ID " + contactId + " 不存在");
                        failCount++;
                        continue;
                    }

                    // 验证所有权
                    if (!addressBook.getUserId().equals(currentUser.getId())) {
                        errors.add("联系人ID " + contactId + " 无权删除");
                        failCount++;
                        continue;
                    }

                    // 软删除
                    addressBook.setDeleted(true);
                    addressBook.setUpdateTime(LocalDateTime.now());
                    addressBookMapper.updateById(addressBook);
                    successCount++;

                } catch (Exception e) {
                    errors.add("联系人ID " + contactId + " 删除失败：" + e.getMessage());
                    failCount++;
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errors", errors);

            return Result.success(result, String.format("批量删除完成：成功%d条，失败%d条", successCount, failCount));

        } catch (Exception e) {
            log.error("批量删除联系人失败: {}", e.getMessage(), e);
            return Result.failed("批量删除联系人失败");
        }
    }

    @Override
    @Transactional
    public Result<?> bindToCard(Long contactId, Long cardId) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 获取联系人
            WalletAddressBook addressBook = addressBookMapper.selectById(contactId);
            if (addressBook == null) {
                return Result.failed("联系人不存在");
            }

            // 验证所有权
            if (!addressBook.getUserId().equals(currentUser.getId())) {
                return Result.failed("无权修改该联系人");
            }

            // 关联名片
            addressBook.setCardId(cardId);
            addressBook.setUpdateTime(LocalDateTime.now());
            addressBookMapper.updateById(addressBook);

            return Result.success("关联名片成功");

        } catch (Exception e) {
            log.error("关联名片失败: {}", e.getMessage(), e);
            return Result.failed("关联名片失败");
        }
    }

    @Override
    @Transactional
    public Result<?> importFromCards(CardImportDTO dto) {
        try {
            // 获取当前用户
            User currentUser = userService.getCurrentUser();
            if (currentUser == null) {
                return Result.failed("用户未登录");
            }

            // 验证名片ID列表
            if (dto.getCardIds() == null || dto.getCardIds().isEmpty()) {
                return Result.failed("名片ID列表不能为空");
            }

            int successCount = 0;
            int failCount = 0;
            List<String> errors = new ArrayList<>();

            for (Long cardId : dto.getCardIds()) {
                try {
                    // TODO: 这里需要从名片中提取钱包地址信息
                    // 由于名片表结构中可能没有钱包字段，这里使用模拟数据

                    // 模拟从名片获取钱包地址
                    String walletAddress = "0x" + cardId + "123456789012345678901234567890";
                    String coinType = "ETH";
                    String contactName = "名片联系人" + cardId;

                    // 检查地址是否已存在
                    WalletAddressBook existing = addressBookMapper.selectByAddressAndCoinType(
                        currentUser.getId(), walletAddress, coinType);

                    if (existing != null && !dto.getOverwrite()) {
                        errors.add("名片ID " + cardId + "：地址已存在");
                        failCount++;
                        continue;
                    }

                    // 创建或更新地址簿记录
                    WalletAddressBook addressBook;
                    if (existing != null) {
                        addressBook = existing;
                    } else {
                        addressBook = new WalletAddressBook();
                        addressBook.setUserId(currentUser.getId());
                        addressBook.setUseCount(0);
                        addressBook.setIsFrequent(false);
                        addressBook.setRiskLevel("LOW");
                        addressBook.setDeleted(false);
                        addressBook.setCreateTime(LocalDateTime.now());
                    }

                    addressBook.setContactName(contactName);
                    addressBook.setWalletAddress(walletAddress);
                    addressBook.setCoinType(coinType);
                    addressBook.setAddressCategory(dto.getDefaultCategory());
                    addressBook.setAddressLabel(dto.getDefaultLabel());
                    addressBook.setCardId(cardId);
                    addressBook.setUpdateTime(LocalDateTime.now());

                    if (existing != null) {
                        addressBookMapper.updateById(addressBook);
                    } else {
                        addressBookMapper.insert(addressBook);
                    }

                    successCount++;

                } catch (Exception e) {
                    errors.add("名片ID " + cardId + " 导入失败：" + e.getMessage());
                    failCount++;
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("successCount", successCount);
            result.put("failCount", failCount);
            result.put("errors", errors);

            return Result.success(result, String.format("从名片导入完成：成功%d条，失败%d条", successCount, failCount));

        } catch (Exception e) {
            log.error("从名片导入地址失败: {}", e.getMessage(), e);
            return Result.failed("从名片导入地址失败");
        }
    }
}
