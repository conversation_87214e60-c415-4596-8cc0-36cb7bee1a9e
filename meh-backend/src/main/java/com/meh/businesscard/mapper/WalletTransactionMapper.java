package com.meh.businesscard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meh.businesscard.entity.WalletTransaction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 钱包交易记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Mapper
public interface WalletTransactionMapper extends BaseMapper<WalletTransaction> {

    /**
     * 分页查询钱包交易记录
     */
    @Select("<script>" +
            "SELECT * FROM t_wallet_transaction WHERE 1=1 " +
            "<if test='walletId != null'>AND wallet_id = #{walletId}</if> " +
            "<if test='userId != null'>AND user_id = #{userId}</if> " +
            "<if test='txType != null and txType != \"\"'>AND tx_type = #{txType}</if> " +
            "<if test='status != null and status != \"\"'>AND status = #{status}</if> " +
            "<if test='coinType != null and coinType != \"\"'>AND coin_type = #{coinType}</if> " +
            "<if test='startTime != null'>AND create_time >= #{startTime}</if> " +
            "<if test='endTime != null'>AND create_time &lt;= #{endTime}</if> " +
            "ORDER BY create_time DESC" +
            "</script>")
    IPage<WalletTransaction> selectTransactionPage(Page<WalletTransaction> page,
                                                   @Param("walletId") Long walletId,
                                                   @Param("userId") Long userId,
                                                   @Param("txType") String txType,
                                                   @Param("status") String status,
                                                   @Param("coinType") String coinType,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 根据交易哈希查询交易
     */
    @Select("SELECT * FROM t_wallet_transaction WHERE tx_hash = #{txHash}")
    WalletTransaction selectByTxHash(@Param("txHash") String txHash);

    /**
     * 获取钱包最近交易记录
     */
    @Select("SELECT * FROM t_wallet_transaction WHERE wallet_id = #{walletId} ORDER BY create_time DESC LIMIT #{limit}")
    List<WalletTransaction> selectRecentTransactions(@Param("walletId") Long walletId, @Param("limit") Integer limit);

    /**
     * 统计钱包交易数量
     */
    @Select("SELECT COUNT(*) FROM t_wallet_transaction WHERE wallet_id = #{walletId}")
    int countByWalletId(@Param("walletId") Long walletId);

    /**
     * 统计钱包交易金额
     */
    @Select("SELECT " +
            "SUM(CASE WHEN tx_type = 'SEND' THEN amount ELSE 0 END) as totalSent, " +
            "SUM(CASE WHEN tx_type = 'RECEIVE' THEN amount ELSE 0 END) as totalReceived, " +
            "SUM(gas_fee) as totalGasFee " +
            "FROM t_wallet_transaction WHERE wallet_id = #{walletId}")
    Map<String, BigDecimal> selectTransactionStats(@Param("walletId") Long walletId);

    /**
     * 获取相关交易（同一地址的其他交易）
     */
    @Select("SELECT * FROM t_wallet_transaction WHERE " +
            "(from_address = #{address} OR to_address = #{address}) " +
            "AND id != #{excludeId} " +
            "ORDER BY create_time DESC LIMIT #{limit}")
    List<WalletTransaction> selectRelatedTransactions(@Param("address") String address,
                                                      @Param("excludeId") Long excludeId,
                                                      @Param("limit") Integer limit);

    /**
     * 获取待确认的交易
     */
    @Select("SELECT * FROM t_wallet_transaction WHERE status = 'PENDING' AND wallet_id = #{walletId}")
    List<WalletTransaction> selectPendingTransactions(@Param("walletId") Long walletId);

    /**
     * 根据地址获取交易记录
     */
    @Select("SELECT * FROM t_wallet_transaction WHERE from_address = #{address} OR to_address = #{address} ORDER BY create_time DESC")
    List<WalletTransaction> selectByAddress(@Param("address") String address);
}
