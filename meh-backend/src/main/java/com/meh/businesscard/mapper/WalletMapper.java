package com.meh.businesscard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meh.businesscard.entity.Wallet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 钱包Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Mapper
public interface WalletMapper extends BaseMapper<Wallet> {

    /**
     * 根据用户ID获取钱包列表
     */
    @Select("SELECT * FROM t_wallet WHERE user_id = #{userId} ORDER BY is_main DESC, create_time DESC")
    List<Wallet> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和币种获取钱包
     */
    @Select("SELECT * FROM t_wallet WHERE user_id = #{userId} AND coin_type = #{coinType}")
    List<Wallet> selectByUserIdAndCoinType(@Param("userId") Long userId, @Param("coinType") String coinType);

    /**
     * 根据钱包地址获取钱包
     */
    @Select("SELECT * FROM t_wallet WHERE wallet_address = #{walletAddress}")
    Wallet selectByWalletAddress(@Param("walletAddress") String walletAddress);

    /**
     * 获取用户的主钱包
     */
    @Select("SELECT * FROM t_wallet WHERE user_id = #{userId} AND is_main = 1 LIMIT 1")
    Wallet selectMainWalletByUserId(@Param("userId") Long userId);

    /**
     * 更新钱包余额
     */
    @Update("UPDATE t_wallet SET balance = #{balance}, last_sync_time = NOW() WHERE id = #{walletId}")
    int updateBalance(@Param("walletId") Long walletId, @Param("balance") BigDecimal balance);

    /**
     * 设置主钱包（先清除其他主钱包标记）
     */
    @Update("UPDATE t_wallet SET is_main = 0 WHERE user_id = #{userId}")
    int clearMainWallet(@Param("userId") Long userId);

    /**
     * 设置指定钱包为主钱包
     */
    @Update("UPDATE t_wallet SET is_main = 1 WHERE id = #{walletId}")
    int setMainWallet(@Param("walletId") Long walletId);

    /**
     * 统计用户钱包数量
     */
    @Select("SELECT COUNT(*) FROM t_wallet WHERE user_id = #{userId}")
    int countByUserId(@Param("userId") Long userId);

    /**
     * 根据状态获取钱包列表
     */
    @Select("SELECT * FROM t_wallet WHERE user_id = #{userId} AND status = #{status}")
    List<Wallet> selectByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);
}
