package com.meh.businesscard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meh.businesscard.entity.WalletAddressBook;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 钱包地址簿Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Mapper
public interface WalletAddressBookMapper extends BaseMapper<WalletAddressBook> {

    /**
     * 分页查询地址簿
     */
    @Select("<script>" +
            "SELECT * FROM t_wallet_address_book WHERE user_id = #{userId} AND deleted = 0 " +
            "<if test='coinType != null and coinType != \"\"'>AND coin_type = #{coinType}</if> " +
            "<if test='category != null and category != \"\"'>AND address_category = #{category}</if> " +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (contact_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR wallet_address LIKE CONCAT('%', #{keyword}, '%') " +
            "OR address_label LIKE CONCAT('%', #{keyword}, '%'))" +
            "</if> " +
            "ORDER BY is_frequent DESC, use_count DESC, create_time DESC" +
            "</script>")
    IPage<WalletAddressBook> selectAddressBookPage(Page<WalletAddressBook> page,
                                                   @Param("userId") Long userId,
                                                   @Param("coinType") String coinType,
                                                   @Param("category") String category,
                                                   @Param("keyword") String keyword);

    /**
     * 获取常用地址
     */
    @Select("<script>" +
            "SELECT * FROM t_wallet_address_book WHERE user_id = #{userId} AND deleted = 0 AND is_frequent = 1 " +
            "<if test='coinType != null and coinType != \"\"'>AND coin_type = #{coinType}</if> " +
            "ORDER BY use_count DESC, last_used_time DESC" +
            "</script>")
    List<WalletAddressBook> selectFrequentAddresses(@Param("userId") Long userId, @Param("coinType") String coinType);

    /**
     * 获取最近使用的地址
     */
    @Select("<script>" +
            "SELECT * FROM t_wallet_address_book WHERE user_id = #{userId} AND deleted = 0 " +
            "<if test='coinType != null and coinType != \"\"'>AND coin_type = #{coinType}</if> " +
            "ORDER BY last_used_time DESC LIMIT #{limit}" +
            "</script>")
    List<WalletAddressBook> selectRecentAddresses(@Param("userId") Long userId,
                                                  @Param("coinType") String coinType,
                                                  @Param("limit") Integer limit);

    /**
     * 根据地址和币种查询
     */
    @Select("SELECT * FROM t_wallet_address_book WHERE user_id = #{userId} AND wallet_address = #{address} AND coin_type = #{coinType} AND deleted = 0")
    WalletAddressBook selectByAddressAndCoinType(@Param("userId") Long userId,
                                                 @Param("address") String address,
                                                 @Param("coinType") String coinType);

    /**
     * 更新使用次数和最后使用时间
     */
    @Update("UPDATE t_wallet_address_book SET use_count = use_count + 1, last_used_time = NOW() WHERE id = #{id}")
    int updateUseCount(@Param("id") Long id);

    /**
     * 根据分类获取地址
     */
    @Select("SELECT * FROM t_wallet_address_book WHERE user_id = #{userId} AND address_category = #{category} AND deleted = 0")
    List<WalletAddressBook> selectByCategory(@Param("userId") Long userId, @Param("category") String category);

    /**
     * 根据标签获取地址
     */
    @Select("SELECT * FROM t_wallet_address_book WHERE user_id = #{userId} AND address_label = #{label} AND deleted = 0")
    List<WalletAddressBook> selectByLabel(@Param("userId") Long userId, @Param("label") String label);

    /**
     * 统计地址簿数量
     */
    @Select("SELECT COUNT(*) FROM t_wallet_address_book WHERE user_id = #{userId} AND deleted = 0")
    int countByUserId(@Param("userId") Long userId);

    /**
     * 获取所有地址标签
     */
    @Select("SELECT DISTINCT address_label FROM t_wallet_address_book WHERE user_id = #{userId} AND deleted = 0 AND address_label IS NOT NULL")
    List<String> selectAllLabels(@Param("userId") Long userId);
}
