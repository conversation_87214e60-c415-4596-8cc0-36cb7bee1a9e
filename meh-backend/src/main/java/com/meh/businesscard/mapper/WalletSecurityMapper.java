package com.meh.businesscard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meh.businesscard.entity.WalletSecurity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 钱包安全设置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Mapper
public interface WalletSecurityMapper extends BaseMapper<WalletSecurity> {

    /**
     * 根据用户ID获取全局安全设置
     */
    @Select("SELECT * FROM t_wallet_security WHERE user_id = #{userId} AND wallet_id IS NULL")
    WalletSecurity selectGlobalSecurityByUserId(@Param("userId") Long userId);

    /**
     * 根据钱包ID获取安全设置
     */
    @Select("SELECT * FROM t_wallet_security WHERE wallet_id = #{walletId}")
    WalletSecurity selectByWalletId(@Param("walletId") Long walletId);

    /**
     * 根据用户ID和钱包ID获取安全设置
     */
    @Select("SELECT * FROM t_wallet_security WHERE user_id = #{userId} AND wallet_id = #{walletId}")
    WalletSecurity selectByUserIdAndWalletId(@Param("userId") Long userId, @Param("walletId") Long walletId);

    /**
     * 获取用户所有安全设置
     */
    @Select("SELECT * FROM t_wallet_security WHERE user_id = #{userId}")
    List<WalletSecurity> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据设备指纹查询
     */
    @Select("SELECT * FROM t_wallet_security WHERE user_id = #{userId} AND device_fingerprint = #{deviceFingerprint}")
    List<WalletSecurity> selectByDeviceFingerprint(@Param("userId") Long userId, @Param("deviceFingerprint") String deviceFingerprint);

    /**
     * 更新最后验证时间
     */
    @Update("UPDATE t_wallet_security SET last_verify_time = NOW() WHERE id = #{id}")
    int updateLastVerifyTime(@Param("id") Long id);

    /**
     * 获取启用多重签名的钱包
     */
    @Select("SELECT * FROM t_wallet_security WHERE user_id = #{userId} AND multi_sig_enabled = 1")
    List<WalletSecurity> selectMultiSigEnabled(@Param("userId") Long userId);

    /**
     * 获取启用生物识别的设置
     */
    @Select("SELECT * FROM t_wallet_security WHERE user_id = #{userId} AND biometric_enabled = 1")
    List<WalletSecurity> selectBiometricEnabled(@Param("userId") Long userId);

    /**
     * 获取启用设备绑定的设置
     */
    @Select("SELECT * FROM t_wallet_security WHERE user_id = #{userId} AND device_binding_enabled = 1")
    List<WalletSecurity> selectDeviceBindingEnabled(@Param("userId") Long userId);

    /**
     * 检查设备是否已绑定
     */
    @Select("SELECT COUNT(*) > 0 FROM t_wallet_security WHERE user_id = #{userId} AND device_fingerprint = #{deviceFingerprint} AND device_binding_enabled = 1")
    boolean isDeviceBound(@Param("userId") Long userId, @Param("deviceFingerprint") String deviceFingerprint);
}
