package com.meh.businesscard.contract;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.Request;
import org.web3j.protocol.core.methods.response.EthCall;
import org.web3j.protocol.core.methods.response.EthGetCode;

import java.math.BigInteger;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * ERC20合约测试类
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@ExtendWith(MockitoExtension.class)
class ERC20ContractTest {

    @Mock
    private Web3j web3j;

    @Mock
    private Request<?, EthCall> ethCallRequest;

    @Mock
    private Request<?, EthGetCode> ethGetCodeRequest;

    private ERC20Contract erc20Contract;
    private final String contractAddress = "******************************************";

    @BeforeEach
    void setUp() {
        erc20Contract = new ERC20Contract(web3j, contractAddress);
    }

    @Test
    void testGetContractAddress() {
        assertEquals(contractAddress, erc20Contract.getContractAddress());
    }

    @Test
    void testCreateTransferData() {
        String toAddress = "0x8ba1f109551bD432803012645Hac136c";
        BigInteger value = new BigInteger("1000000000000000000"); // 1 Token (18 decimals)

        String transferData = erc20Contract.createTransferData(toAddress, value);

        assertNotNull(transferData);
        assertTrue(transferData.startsWith("0xa9059cbb")); // transfer function selector
        assertTrue(transferData.length() > 10); // Should contain function selector + parameters
    }

    @Test
    void testCreateApproveData() {
        String spenderAddress = "0x8ba1f109551bD432803012645Hac136c";
        BigInteger value = new BigInteger("1000000000000000000"); // 1 Token (18 decimals)

        String approveData = erc20Contract.createApproveData(spenderAddress, value);

        assertNotNull(approveData);
        assertTrue(approveData.startsWith("0x095ea7b3")); // approve function selector
        assertTrue(approveData.length() > 10); // Should contain function selector + parameters
    }

    @Test
    void testCreateTransferFromData() {
        String fromAddress = "******************************************";
        String toAddress = "0x8ba1f109551bD432803012645Hac136c";
        BigInteger value = new BigInteger("1000000000000000000"); // 1 Token (18 decimals)

        String transferFromData = erc20Contract.createTransferFromData(fromAddress, toAddress, value);

        assertNotNull(transferFromData);
        assertTrue(transferFromData.startsWith("0x23b872dd")); // transferFrom function selector
        assertTrue(transferFromData.length() > 10); // Should contain function selector + parameters
    }

    @Test
    void testIsContractValidWithValidContract() throws Exception {
        // Mock valid contract code
        EthGetCode ethGetCode = new EthGetCode();
        ethGetCode.setResult("0x608060405234801561001057600080fd5b50..."); // Non-empty bytecode

        when(web3j.ethGetCode(eq(contractAddress), any())).thenReturn(ethGetCodeRequest);
        when(ethGetCodeRequest.sendAsync()).thenReturn(CompletableFuture.completedFuture(ethGetCode));

        CompletableFuture<Boolean> result = erc20Contract.isContractValid();
        assertTrue(result.get());
    }

    @Test
    void testIsContractValidWithInvalidContract() throws Exception {
        // Mock empty contract code
        EthGetCode ethGetCode = new EthGetCode();
        ethGetCode.setResult("0x"); // Empty bytecode

        when(web3j.ethGetCode(eq(contractAddress), any())).thenReturn(ethGetCodeRequest);
        when(ethGetCodeRequest.sendAsync()).thenReturn(CompletableFuture.completedFuture(ethGetCode));

        CompletableFuture<Boolean> result = erc20Contract.isContractValid();
        assertFalse(result.get());
    }

    @Test
    void testBalanceOfFunctionEncoding() {
        String ownerAddress = "******************************************";
        
        // Test that we can create the function call without errors
        assertDoesNotThrow(() -> {
            CompletableFuture<BigInteger> future = erc20Contract.balanceOf(ownerAddress);
            assertNotNull(future);
        });
    }

    @Test
    void testAllowanceFunctionEncoding() {
        String ownerAddress = "******************************************";
        String spenderAddress = "0x8ba1f109551bD432803012645Hac136c";
        
        // Test that we can create the function call without errors
        assertDoesNotThrow(() -> {
            CompletableFuture<BigInteger> future = erc20Contract.allowance(ownerAddress, spenderAddress);
            assertNotNull(future);
        });
    }

    @Test
    void testNameFunctionEncoding() {
        // Test that we can create the function call without errors
        assertDoesNotThrow(() -> {
            CompletableFuture<String> future = erc20Contract.name();
            assertNotNull(future);
        });
    }

    @Test
    void testSymbolFunctionEncoding() {
        // Test that we can create the function call without errors
        assertDoesNotThrow(() -> {
            CompletableFuture<String> future = erc20Contract.symbol();
            assertNotNull(future);
        });
    }

    @Test
    void testDecimalsFunctionEncoding() {
        // Test that we can create the function call without errors
        assertDoesNotThrow(() -> {
            CompletableFuture<BigInteger> future = erc20Contract.decimals();
            assertNotNull(future);
        });
    }

    @Test
    void testTotalSupplyFunctionEncoding() {
        // Test that we can create the function call without errors
        assertDoesNotThrow(() -> {
            CompletableFuture<BigInteger> future = erc20Contract.totalSupply();
            assertNotNull(future);
        });
    }

    @Test
    void testEstimateGasFunctionEncoding() {
        String fromAddress = "******************************************";
        String data = "0xa9059cbb000000000000000000000000742d35cc6634c0532925a3b8d4c9db96c4b4d8b60000000000000000000000000000000000000000000000000de0b6b3a7640000";
        
        // Test that we can create the function call without errors
        assertDoesNotThrow(() -> {
            CompletableFuture<BigInteger> future = erc20Contract.estimateGas(fromAddress, data);
            assertNotNull(future);
        });
    }
}
