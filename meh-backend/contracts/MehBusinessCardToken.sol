// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Burnable.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

/**
 * MEH Business Card Token (MBC)
 * 
 * 这是MEH电子名片项目的专用Token，部署在Polygon网络上
 * 具有以下特性：
 * - ERC20标准Token
 * - 可暂停功能
 * - 可销毁功能
 * - 所有者权限管理
 * - 防重入攻击
 * - 积分奖励功能
 * - 名片绑定功能
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
contract MehBusinessCardToken is ERC20, ERC20Burnable, ERC20Pausable, Ownable, ReentrancyGuard {
    
    // Token基本信息
    uint8 private constant DECIMALS = 18;
    uint256 private constant INITIAL_SUPPLY = ********** * 10**DECIMALS; // 10亿Token
    
    // 积分奖励配置
    mapping(string => uint256) public rewardRules;
    
    // 用户积分记录
    mapping(address => uint256) public userPoints;
    
    // 名片绑定记录
    mapping(address => uint256) public boundCards;
    mapping(uint256 => address) public cardOwners;
    
    // 每日奖励限制
    mapping(address => mapping(string => uint256)) public dailyRewardCount;
    mapping(address => uint256) public lastRewardDate;
    
    // 事件定义
    event PointsAwarded(address indexed user, string action, uint256 points);
    event CardBound(address indexed user, uint256 cardId);
    event CardUnbound(address indexed user, uint256 cardId);
    event RewardRuleUpdated(string action, uint256 points);
    
    /**
     * 构造函数
     */
    constructor() ERC20("MEH Business Card Token", "MBC") {
        // 铸造初始供应量给合约部署者
        _mint(msg.sender, INITIAL_SUPPLY);
        
        // 初始化积分奖励规则
        _initializeRewardRules();
    }
    
    /**
     * 初始化积分奖励规则
     */
    function _initializeRewardRules() private {
        rewardRules["daily_login"] = 5 * 10**DECIMALS;
        rewardRules["complete_profile"] = 20 * 10**DECIMALS;
        rewardRules["create_card"] = 50 * 10**DECIMALS;
        rewardRules["share_card"] = 10 * 10**DECIMALS;
        rewardRules["like_card"] = 2 * 10**DECIMALS;
        rewardRules["comment_card"] = 5 * 10**DECIMALS;
        rewardRules["add_contact"] = 5 * 10**DECIMALS;
        rewardRules["invite_friend"] = 100 * 10**DECIMALS;
    }
    
    /**
     * 获取Token精度
     */
    function decimals() public pure override returns (uint8) {
        return DECIMALS;
    }
    
    /**
     * 奖励用户积分
     * @param user 用户地址
     * @param action 行为类型
     */
    function awardPoints(address user, string memory action) external onlyOwner nonReentrant {
        require(user != address(0), "Invalid user address");
        require(rewardRules[action] > 0, "Invalid action");
        
        uint256 points = rewardRules[action];
        
        // 检查每日奖励限制
        if (_isDailyLimited(action)) {
            require(_checkDailyLimit(user, action), "Daily reward limit exceeded");
            _updateDailyCount(user, action);
        }
        
        // 铸造Token作为积分奖励
        _mint(user, points);
        userPoints[user] += points;
        
        emit PointsAwarded(user, action, points);
    }
    
    /**
     * 批量奖励积分
     * @param users 用户地址数组
     * @param action 行为类型
     */
    function batchAwardPoints(address[] memory users, string memory action) external onlyOwner {
        require(users.length > 0, "Empty users array");
        require(rewardRules[action] > 0, "Invalid action");
        
        for (uint256 i = 0; i < users.length; i++) {
            if (users[i] != address(0)) {
                awardPoints(users[i], action);
            }
        }
    }
    
    /**
     * 绑定名片到钱包地址
     * @param cardId 名片ID
     */
    function bindCard(uint256 cardId) external {
        require(cardId > 0, "Invalid card ID");
        require(boundCards[msg.sender] == 0, "Address already bound to a card");
        require(cardOwners[cardId] == address(0), "Card already bound to an address");
        
        boundCards[msg.sender] = cardId;
        cardOwners[cardId] = msg.sender;
        
        // 奖励绑定名片积分
        awardPoints(msg.sender, "create_card");
        
        emit CardBound(msg.sender, cardId);
    }
    
    /**
     * 解绑名片
     */
    function unbindCard() external {
        uint256 cardId = boundCards[msg.sender];
        require(cardId > 0, "No card bound to this address");
        
        delete boundCards[msg.sender];
        delete cardOwners[cardId];
        
        emit CardUnbound(msg.sender, cardId);
    }
    
    /**
     * 更新奖励规则
     * @param action 行为类型
     * @param points 奖励积分
     */
    function updateRewardRule(string memory action, uint256 points) external onlyOwner {
        rewardRules[action] = points;
        emit RewardRuleUpdated(action, points);
    }
    
    /**
     * 获取用户绑定的名片ID
     * @param user 用户地址
     */
    function getUserCard(address user) external view returns (uint256) {
        return boundCards[user];
    }
    
    /**
     * 获取名片所有者地址
     * @param cardId 名片ID
     */
    function getCardOwner(uint256 cardId) external view returns (address) {
        return cardOwners[cardId];
    }
    
    /**
     * 获取用户总积分
     * @param user 用户地址
     */
    function getUserPoints(address user) external view returns (uint256) {
        return userPoints[user];
    }
    
    /**
     * 检查是否为每日限制的行为
     */
    function _isDailyLimited(string memory action) private pure returns (bool) {
        return (
            keccak256(bytes(action)) == keccak256(bytes("daily_login")) ||
            keccak256(bytes(action)) == keccak256(bytes("like_card")) ||
            keccak256(bytes(action)) == keccak256(bytes("comment_card")) ||
            keccak256(bytes(action)) == keccak256(bytes("share_card"))
        );
    }
    
    /**
     * 检查每日奖励限制
     */
    function _checkDailyLimit(address user, string memory action) private view returns (bool) {
        uint256 today = block.timestamp / 86400; // 当前日期
        uint256 userLastDate = lastRewardDate[user] / 86400;
        
        if (today > userLastDate) {
            return true; // 新的一天，重置计数
        }
        
        // 检查具体行为的每日限制
        uint256 dailyLimit = _getDailyLimit(action);
        return dailyRewardCount[user][action] < dailyLimit;
    }
    
    /**
     * 获取每日奖励限制
     */
    function _getDailyLimit(string memory action) private pure returns (uint256) {
        if (keccak256(bytes(action)) == keccak256(bytes("daily_login"))) return 1;
        if (keccak256(bytes(action)) == keccak256(bytes("like_card"))) return 10;
        if (keccak256(bytes(action)) == keccak256(bytes("comment_card"))) return 5;
        if (keccak256(bytes(action)) == keccak256(bytes("share_card"))) return 3;
        return 999999; // 无限制
    }
    
    /**
     * 更新每日计数
     */
    function _updateDailyCount(address user, string memory action) private {
        uint256 today = block.timestamp / 86400;
        uint256 userLastDate = lastRewardDate[user] / 86400;
        
        if (today > userLastDate) {
            // 新的一天，重置所有计数
            delete dailyRewardCount[user]["daily_login"];
            delete dailyRewardCount[user]["like_card"];
            delete dailyRewardCount[user]["comment_card"];
            delete dailyRewardCount[user]["share_card"];
            lastRewardDate[user] = block.timestamp;
        }
        
        dailyRewardCount[user][action]++;
    }
    
    /**
     * 暂停合约
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * 恢复合约
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * 铸造Token（仅所有者）
     * @param to 接收地址
     * @param amount 铸造数量
     */
    function mint(address to, uint256 amount) external onlyOwner {
        _mint(to, amount);
    }
    
    /**
     * 重写_beforeTokenTransfer以支持暂停功能
     */
    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 amount
    ) internal override(ERC20, ERC20Pausable) {
        super._beforeTokenTransfer(from, to, amount);
    }
}
