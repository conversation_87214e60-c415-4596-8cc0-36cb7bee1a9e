# MEH电子名片微信小程序

## 项目概述

MEH电子名片是一款基于微信小程序的电子名片管理应用，旨在为用户提供便捷的电子名片创建、管理、分享和社交功能。本项目采用前后端分离架构，前端使用UniApp开发，后端使用Spring Boot开发，支持Docker容器化部署。

## 技术栈

### 前端
- 框架：UniApp + Vue.js
- UI组件：uView UI
- 状态管理：Vuex
- 请求封装：自定义API模块

### 后端
- 框架：Spring Boot 2.7.x
- ORM：MyBatis-Plus
- 安全框架：Spring Security + JWT
- 缓存：Redis
- 数据库：MySQL 8.0
- 部署：Docker + Docker Compose

## 功能特点

### 1. 用户认证与授权
- 支持手机号密码登录和微信小程序登录
- 基于JWT的认证机制
- 权限控制基于Spring Security实现

### 2. 名片管理
- 创建、编辑、删除、查询名片
- 名片模板选择功能
- 资源和项目展示功能
- 名片二维码生成与分享

### 3. 通讯录管理
- 联系人的增删改查
- 联系人分组管理
- 星标联系人功能

### 4. 社交互动
- 名片点赞、评论功能
- 访客记录统计
- 在线咨询功能

### 5. 积分系统
- 多种积分获取途径
- 积分兑换商城
- 积分变动日志

### 6. 营销推广
- 裂变营销活动
- 邀请奖励机制

## 项目结构

### 前端结构
```
meh-frontend/
├── api/                # API请求封装
├── components/         # 公共组件
├── pages/              # 页面文件
│   ├── index/          # 首页
│   ├── login/          # 登录页
│   ├── card/           # 名片相关页面
│   ├── contact/        # 通讯录相关页面
│   ├── points/         # 积分相关页面
│   ├── chat/           # 在线咨询相关页面
│   └── user/           # 用户中心相关页面
├── static/             # 静态资源
├── store/              # Vuex状态管理
├── utils/              # 工具函数
├── main.js             # 应用入口文件
└── pages.json          # 页面配置
```

### 后端结构
```
meh-backend/
├── src/
│   ├── main/
│   │   ├── java/com/meh/businesscard/
│   │   │   ├── common/           # 公共模块
│   │   │   │   ├── api/          # 通用API响应
│   │   │   │   ├── config/       # 配置类
│   │   │   │   ├── exception/    # 异常处理
│   │   │   │   └── util/         # 工具类
│   │   │   ├── controller/       # 控制器
│   │   │   ├── dto/              # 数据传输对象
│   │   │   ├── entity/           # 实体类
│   │   │   ├── mapper/           # MyBatis映射接口
│   │   │   ├── service/          # 服务接口
│   │   │   │   └── impl/         # 服务实现
│   │   │   └── BusinessCardApplication.java  # 应用入口
│   │   └── resources/
│   │       ├── mapper/           # MyBatis XML映射文件
│   │       ├── application.yml   # 应用配置
│   │       ├── application-dev.yml  # 开发环境配置
│   │       └── application-prod.yml # 生产环境配置
│   └── test/                     # 测试代码
├── sql/                          # SQL脚本
├── Dockerfile                    # Docker构建文件
└── docker-compose.yml            # Docker Compose配置
```

## 数据库设计

系统包含以下主要数据表：
- 用户表 (t_user)
- 用户积分表 (t_user_points)
- 用户积分变动日志表 (t_user_points_log)
- 名片表 (t_card)
- 名片模板表 (t_card_template)
- 名片访问记录表 (t_card_visit_log)
- 名片点赞表 (t_card_like)
- 名片评论表 (t_card_comment)
- 联系人表 (t_contact)
- 联系人分组表 (t_contact_group)
- 积分商品表 (t_points_goods)
- 积分商品分类表 (t_points_goods_category)
- 积分兑换记录表 (t_points_exchange)
- 活动表 (t_activity)
- 邀请记录表 (t_invite)
- 聊天会话表 (t_chat)
- 聊天消息表 (t_chat_message)

## 安装与部署

### 环境要求
- Docker 19.03+
- Docker Compose 1.25+
- 微信开发者工具（前端开发与调试）

### 后端部署

1. 克隆项目代码
```bash
git clone https://github.com/your-repo/meh-business-card.git
cd meh-business-card/meh-backend
```

2. 使用Docker Compose部署
```bash
docker-compose up -d
```

3. 验证部署
```bash
curl http://localhost:8080/actuator/health
```

### 前端部署

1. 安装依赖
```bash
cd ../meh-frontend
npm install
```

2. 开发模式
```bash
npm run dev:mp-weixin
```

3. 生产构建
```bash
npm run build:mp-weixin
```

4. 在微信开发者工具中导入项目（dist/dev/mp-weixin目录）

## 项目亮点

1. **高质量代码规范**：所有代码均采用中文注释，遵循行业最佳实践，确保可读性和可维护性。

2. **完善的容器化部署**：提供了Docker和Docker Compose配置，支持一键部署，简化运维工作。

3. **细致的积分系统**：实现了丰富的积分获取途径和规则，提高用户活跃度和粘性。

4. **资源与项目展示**：针对个体户用户需求，特别设计了资源和项目展示功能，方便用户展示自己的优势。

5. **全面的社交互动**：提供点赞、评论、在线咨询等多种社交功能，增强用户间的连接。

6. **响应式设计**：前端页面采用响应式设计，适配不同尺寸的设备，提供良好的用户体验。

## 联系与支持

如有任何问题或建议，请通过以下方式联系我们：

- 客服电话：400-123-4567
- 客服邮箱：<EMAIL>
- 在线咨询：小程序内"个人中心"-"在线客服"



http://localhost:8080/api/v3/api-docs
http://localhost:8080/api/swagger-ui/index.html