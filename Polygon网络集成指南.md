# MEH电子名片 - Polygon网络集成指南

## 🎯 **项目概述**

MEH电子名片项目已成功从以太坊网络迁移到Polygon网络，实现了以下目标：

- ✅ **降低Gas费用**：Polygon网络的交易费用比以太坊低99%以上
- ✅ **提升交易速度**：交易确认时间从15秒降低到2秒
- ✅ **更好的用户体验**：用户使用区块链钱包的成本大幅降低
- ✅ **项目专用Token**：发行MEH Business Card Token (MBC)用于积分奖励

## 🏗️ **技术架构**

### 后端架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Spring Boot   │    │   Polygon RPC   │    │  Smart Contract │
│   Application   │◄──►│     Node        │◄──►│   (MBC Token)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     MySQL       │    │   Web3j Library │    │  Polygon Mumbai │
│   Database      │    │                 │    │   Testnet       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 前端架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   uni-app       │    │   Polygon API   │    │   Wallet UI     │
│   Framework     │◄──►│   Integration   │◄──►│   Components    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 **快速开始**

### 1. 环境准备

#### 后端环境
```bash
# 确保Java 11+和Maven已安装
cd meh-backend

# 安装依赖
mvn clean install

# 配置Polygon网络
# 编辑 src/main/resources/application.yml
```

#### 前端环境
```bash
# 确保Node.js 14+已安装
cd meh-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev:mp-weixin
```

### 2. 配置Polygon网络

#### 后端配置 (application.yml)
```yaml
polygon:
  # 测试网配置
  testnet:
    rpc-url: https://rpc-mumbai.maticvigil.com
    chain-id: 80001
    network-name: Polygon Mumbai Testnet
    currency-symbol: MATIC
    block-explorer: https://mumbai.polygonscan.com
    
  # 默认使用测试网
  default-network: testnet
  
  # MEH Token配置
  meh-token:
    name: MEH Business Card Token
    symbol: MBC
    decimals: 18
    initial-supply: 1000000000
    contract-address: "" # 部署后填入
```

#### 前端配置 (config/index.js)
```javascript
polygon: {
  testnet: {
    chainId: 80001,
    name: 'Polygon Mumbai Testnet',
    currency: 'MATIC',
    rpcUrl: 'https://rpc-mumbai.maticvigil.com',
    blockExplorer: 'https://mumbai.polygonscan.com'
  },
  currentNetwork: 'testnet'
}
```

## 💰 **MEH Token (MBC) 功能**

### Token基本信息
- **名称**: MEH Business Card Token
- **符号**: MBC
- **精度**: 18位小数
- **总供应量**: 10亿枚
- **网络**: Polygon (Mumbai测试网)

### 积分奖励规则
```javascript
{
  "daily_login": "5 MBC",      // 每日登录
  "complete_profile": "20 MBC", // 完善资料
  "create_card": "50 MBC",     // 创建名片
  "share_card": "10 MBC",      // 分享名片
  "like_card": "2 MBC",        // 点赞名片
  "comment_card": "5 MBC",     // 评论名片
  "add_contact": "5 MBC",      // 添加联系人
  "invite_friend": "100 MBC"   // 邀请好友
}
```

### 名片绑定功能
- 每个钱包地址可以绑定一张名片
- 绑定后自动获得50 MBC奖励
- 支持解绑和重新绑定

## 🔧 **API接口**

### Polygon网络接口

#### 1. 生成钱包地址
```http
POST /api/polygon/generate-address
Content-Type: application/json

{
  "mnemonic": "your mnemonic words here",
  "password": "optional password"
}
```

#### 2. 获取MATIC余额
```http
GET /api/polygon/balance/matic/{address}
```

#### 3. 获取Token余额
```http
GET /api/polygon/balance/token/{address}?contractAddress={tokenContract}
```

#### 4. 发送MATIC转账
```http
POST /api/polygon/transfer/matic
Content-Type: application/json

{
  "fromAddress": "0x...",
  "toAddress": "0x...",
  "amount": "1.5",
  "privateKey": "your-private-key"
}
```

#### 5. 发送Token转账
```http
POST /api/polygon/transfer/token
Content-Type: application/json

{
  "fromAddress": "0x...",
  "toAddress": "0x...",
  "amount": "100",
  "contractAddress": "0x...",
  "privateKey": "your-private-key"
}
```

#### 6. 估算Gas费用
```http
POST /api/polygon/estimate-gas
Content-Type: application/json

{
  "fromAddress": "0x...",
  "toAddress": "0x...",
  "amount": "1.0",
  "contractAddress": "0x..." // 可选
}
```

### MEH Token接口

#### 1. 部署Token合约
```http
POST /api/polygon/deploy-meh-token
Content-Type: application/json

{
  "deployerPrivateKey": "your-private-key"
}
```

#### 2. 铸造Token
```http
POST /api/polygon/mint-meh-token
Content-Type: application/json

{
  "toAddress": "0x...",
  "amount": "100",
  "ownerPrivateKey": "your-private-key"
}
```

#### 3. 获取Token信息
```http
GET /api/polygon/meh-token-info
```

## 🔐 **安全考虑**

### 私钥管理
- ❌ **永远不要**在服务器存储用户私钥
- ❌ **永远不要**在日志中记录私钥
- ✅ 私钥仅在客户端本地加密存储
- ✅ 交易签名在客户端完成

### 助记词安全
- ✅ 使用标准BIP39助记词
- ✅ 提供安全备份指导
- ✅ 警告用户截图风险
- ✅ 建议手写记录

### 网络安全
- ✅ 使用HTTPS通信
- ✅ API接口认证
- ✅ 交易参数验证
- ✅ Gas费用限制

## 🧪 **测试指南**

### 1. 获取测试MATIC
访问 [Polygon Faucet](https://faucet.polygon.technology/) 获取免费的测试MATIC

### 2. 测试钱包功能
```bash
# 启动后端服务
cd meh-backend
mvn spring-boot:run

# 启动前端服务
cd meh-frontend
npm run dev:mp-weixin
```

### 3. 测试流程
1. 创建钱包 → 生成助记词 → 获得Polygon地址
2. 获取测试MATIC → 检查余额
3. 发送转账 → 验证交易
4. 创建名片 → 绑定钱包 → 获得MBC奖励

## 📊 **性能对比**

| 指标 | 以太坊主网 | Polygon网络 | 改善幅度 |
|------|------------|-------------|----------|
| 交易费用 | $10-50 | $0.01-0.1 | 99%+ ↓ |
| 确认时间 | 15秒-5分钟 | 2-5秒 | 90%+ ↓ |
| TPS | 15 | 7000+ | 460倍 ↑ |
| 用户体验 | 较差 | 优秀 | 显著提升 |

## 🚀 **部署指南**

### 1. 智能合约部署
```bash
# 设置部署者私钥环境变量
export DEPLOYER_PRIVATE_KEY="your-private-key"

# 运行部署脚本
cd meh-backend
node scripts/deploy-contract.js
```

### 2. 更新配置
部署完成后，更新配置文件中的合约地址：
```yaml
polygon:
  meh-token:
    contract-address: "0x..." # 填入部署后的合约地址
```

### 3. 生产环境配置
```yaml
polygon:
  default-network: mainnet # 切换到主网
  mainnet:
    rpc-url: https://polygon-rpc.com
    chain-id: 137
```

## 📞 **技术支持**

如有问题，请联系：
- **开发者**: yanhaishui
- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/yanhaishui/Meh-BusinessCard

---

**更新时间**: 2025年6月13日  
**版本**: v1.0.0  
**网络**: Polygon Mumbai测试网
