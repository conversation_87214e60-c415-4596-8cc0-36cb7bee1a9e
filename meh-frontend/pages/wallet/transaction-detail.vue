<template>
  <view class="detail-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons type="left" size="24" color="#333" class="back-icon" @click="goBack"></uni-icons>
      <text class="nav-title">交易详情</text>
      <uni-icons type="share" size="24" color="#333" class="share-icon" @click="shareTransaction"></uni-icons>
    </view>

    <!-- 交易状态卡片 -->
    <view class="status-card">
      <view class="status-header">
        <view class="status-icon" :class="transaction.status.toLowerCase()">
          <uni-icons 
            :type="getStatusIcon(transaction.status)" 
            size="32" 
            :color="getStatusColor(transaction.status)"
          ></uni-icons>
        </view>
        
        <view class="status-info">
          <text class="status-text">{{ getStatusText(transaction.status) }}</text>
          <text class="status-desc">{{ getStatusDescription(transaction.status) }}</text>
        </view>
      </view>

      <!-- 交易金额 -->
      <view class="amount-section">
        <text class="amount-label">{{ transaction.txType === 'SEND' ? '转出金额' : '转入金额' }}</text>
        <view class="amount-display">
          <text class="amount-value" :class="transaction.txType.toLowerCase()">
            {{ transaction.txType === 'SEND' ? '-' : '+' }}{{ transaction.amount }}
          </text>
          <text class="amount-currency">{{ transaction.coinType }}</text>
        </view>
        <text class="amount-fiat" v-if="transaction.fiatValue">
          ≈ ¥{{ transaction.fiatValue }}
        </text>
      </view>

      <!-- 进度条 -->
      <view class="progress-section" v-if="transaction.status === 'PENDING'">
        <view class="progress-info">
          <text class="progress-text">确认进度</text>
          <text class="progress-count">{{ transaction.confirmations || 0 }}/{{ requiredConfirmations }}</text>
        </view>
        <view class="progress-bar">
          <view 
            class="progress-fill" 
            :style="{ width: progressPercentage + '%' }"
          ></view>
        </view>
        <text class="progress-desc">预计还需 {{ estimatedTime }} 分钟</text>
      </view>
    </view>

    <!-- 交易信息 -->
    <view class="transaction-info">
      <view class="info-section">
        <text class="section-title">交易信息</text>
        
        <view class="info-list">
          <!-- 交易类型 -->
          <view class="info-item">
            <text class="info-label">交易类型</text>
            <text class="info-value">{{ getTxTypeName(transaction.txType) }}</text>
          </view>

          <!-- 交易时间 -->
          <view class="info-item">
            <text class="info-label">交易时间</text>
            <text class="info-value">{{ formatDateTime(transaction.createTime) }}</text>
          </view>

          <!-- 发送方 -->
          <view class="info-item">
            <text class="info-label">发送方</text>
            <view class="address-container">
              <text class="address-text">{{ formatAddress(transaction.fromAddress) }}</text>
              <view class="address-actions">
                <uni-icons type="copy" size="16" color="#667eea" @click="copyAddress(transaction.fromAddress)"></uni-icons>
                <uni-icons type="eye" size="16" color="#667eea" @click="viewAddress(transaction.fromAddress)"></uni-icons>
              </view>
            </view>
          </view>

          <!-- 接收方 -->
          <view class="info-item">
            <text class="info-label">接收方</text>
            <view class="address-container">
              <text class="address-text">{{ formatAddress(transaction.toAddress) }}</text>
              <view class="address-actions">
                <uni-icons type="copy" size="16" color="#667eea" @click="copyAddress(transaction.toAddress)"></uni-icons>
                <uni-icons type="eye" size="16" color="#667eea" @click="viewAddress(transaction.toAddress)"></uni-icons>
              </view>
            </view>
          </view>

          <!-- 交易备注 -->
          <view class="info-item" v-if="transaction.memo">
            <text class="info-label">交易备注</text>
            <text class="info-value">{{ transaction.memo }}</text>
          </view>

          <!-- 网络费用 -->
          <view class="info-item" v-if="transaction.gasFee">
            <text class="info-label">网络费用</text>
            <view class="fee-container">
              <text class="fee-value">{{ transaction.gasFee }} {{ transaction.coinType }}</text>
              <text class="fee-fiat" v-if="transaction.gasFiatValue">≈ ¥{{ transaction.gasFiatValue }}</text>
            </view>
          </view>

          <!-- Gas Price -->
          <view class="info-item" v-if="transaction.gasPrice">
            <text class="info-label">Gas Price</text>
            <text class="info-value">{{ transaction.gasPrice }} Gwei</text>
          </view>

          <!-- Gas Limit -->
          <view class="info-item" v-if="transaction.gasLimit">
            <text class="info-label">Gas Limit</text>
            <text class="info-value">{{ transaction.gasLimit }}</text>
          </view>

          <!-- Nonce -->
          <view class="info-item" v-if="transaction.nonce !== undefined">
            <text class="info-label">Nonce</text>
            <text class="info-value">{{ transaction.nonce }}</text>
          </view>
        </view>
      </view>

      <!-- 区块链信息 -->
      <view class="info-section" v-if="transaction.txHash">
        <text class="section-title">区块链信息</text>
        
        <view class="info-list">
          <!-- 交易哈希 -->
          <view class="info-item">
            <text class="info-label">交易哈希</text>
            <view class="hash-container">
              <text class="hash-text">{{ transaction.txHash }}</text>
              <view class="hash-actions">
                <uni-icons type="copy" size="16" color="#667eea" @click="copyHash(transaction.txHash)"></uni-icons>
                <uni-icons type="link" size="16" color="#667eea" @click="openBlockExplorer(transaction.txHash)"></uni-icons>
              </view>
            </view>
          </view>

          <!-- 区块高度 -->
          <view class="info-item" v-if="transaction.blockNumber">
            <text class="info-label">区块高度</text>
            <text class="info-value"># {{ transaction.blockNumber }}</text>
          </view>

          <!-- 区块哈希 -->
          <view class="info-item" v-if="transaction.blockHash">
            <text class="info-label">区块哈希</text>
            <view class="hash-container">
              <text class="hash-text">{{ formatHash(transaction.blockHash) }}</text>
              <uni-icons type="copy" size="16" color="#667eea" @click="copyHash(transaction.blockHash)"></uni-icons>
            </view>
          </view>

          <!-- 确认数 -->
          <view class="info-item" v-if="transaction.confirmations !== undefined">
            <text class="info-label">确认数</text>
            <text class="info-value">{{ transaction.confirmations }}</text>
          </view>

          <!-- 交易索引 -->
          <view class="info-item" v-if="transaction.transactionIndex !== undefined">
            <text class="info-label">交易索引</text>
            <text class="info-value">{{ transaction.transactionIndex }}</text>
          </view>
        </view>
      </view>

      <!-- 合约信息 -->
      <view class="info-section" v-if="transaction.contractAddress">
        <text class="section-title">合约信息</text>
        
        <view class="info-list">
          <!-- 合约地址 -->
          <view class="info-item">
            <text class="info-label">合约地址</text>
            <view class="address-container">
              <text class="address-text">{{ formatAddress(transaction.contractAddress) }}</text>
              <view class="address-actions">
                <uni-icons type="copy" size="16" color="#667eea" @click="copyAddress(transaction.contractAddress)"></uni-icons>
                <uni-icons type="eye" size="16" color="#667eea" @click="viewContract(transaction.contractAddress)"></uni-icons>
              </view>
            </view>
          </view>

          <!-- 代币信息 -->
          <view class="info-item" v-if="transaction.tokenInfo">
            <text class="info-label">代币信息</text>
            <view class="token-info">
              <image class="token-icon" :src="getCoinIcon(transaction.tokenInfo.symbol)" mode="aspectFit"></image>
              <view class="token-details">
                <text class="token-name">{{ transaction.tokenInfo.name }}</text>
                <text class="token-symbol">{{ transaction.tokenInfo.symbol }}</text>
              </view>
            </view>
          </view>

          <!-- 输入数据 -->
          <view class="info-item" v-if="transaction.inputData">
            <text class="info-label">输入数据</text>
            <view class="data-container">
              <textarea 
                class="data-text" 
                :value="transaction.inputData" 
                readonly
              ></textarea>
              <uni-icons type="copy" size="16" color="#667eea" @click="copyData(transaction.inputData)"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <view class="action-grid">
        <!-- 重新发送 -->
        <view class="action-item" v-if="canResend" @click="resendTransaction">
          <uni-icons type="refresh" size="24" color="#667eea"></uni-icons>
          <text class="action-text">重新发送</text>
        </view>

        <!-- 加速交易 -->
        <view class="action-item" v-if="canSpeedUp" @click="speedUpTransaction">
          <uni-icons type="forward" size="24" color="#FFA726"></uni-icons>
          <text class="action-text">加速交易</text>
        </view>

        <!-- 取消交易 -->
        <view class="action-item" v-if="canCancel" @click="cancelTransaction">
          <uni-icons type="close" size="24" color="#FF6B6B"></uni-icons>
          <text class="action-text">取消交易</text>
        </view>

        <!-- 查看收据 -->
        <view class="action-item" @click="viewReceipt">
          <uni-icons type="receipt" size="24" color="#4ECDC4"></uni-icons>
          <text class="action-text">查看收据</text>
        </view>

        <!-- 分享交易 -->
        <view class="action-item" @click="shareTransaction">
          <uni-icons type="share" size="24" color="#667eea"></uni-icons>
          <text class="action-text">分享</text>
        </view>

        <!-- 举报交易 -->
        <view class="action-item" @click="reportTransaction">
          <uni-icons type="flag" size="24" color="#FF6B6B"></uni-icons>
          <text class="action-text">举报</text>
        </view>
      </view>
    </view>

    <!-- 相关交易 -->
    <view class="related-section" v-if="relatedTransactions.length > 0">
      <view class="section-header">
        <text class="section-title">相关交易</text>
        <text class="section-action" @click="viewAllRelated">查看全部</text>
      </view>

      <view class="related-list">
        <view 
          class="related-item" 
          v-for="relatedTx in relatedTransactions" 
          :key="relatedTx.id"
          @click="openTransaction(relatedTx.id)"
        >
          <view class="related-icon">
            <uni-icons 
              :type="getTxIcon(relatedTx.txType)" 
              size="16" 
              :color="getTxIconColor(relatedTx.txType)"
            ></uni-icons>
          </view>
          
          <view class="related-info">
            <text class="related-type">{{ getTxTypeName(relatedTx.txType) }}</text>
            <text class="related-time">{{ formatTime(relatedTx.createTime) }}</text>
          </view>

          <text class="related-amount" :class="relatedTx.txType.toLowerCase()">
            {{ relatedTx.txType === 'SEND' ? '-' : '+' }}{{ relatedTx.amount }} {{ relatedTx.coinType }}
          </text>
        </view>
      </view>
    </view>

    <!-- 分享弹窗 -->
    <uni-popup ref="sharePopup" type="bottom">
      <view class="share-popup">
        <view class="share-header">
          <text class="share-title">分享交易</text>
          <uni-icons type="close" size="24" @click="closeSharePopup"></uni-icons>
        </view>
        
        <view class="share-options">
          <view class="share-option" @click="shareToWeChat">
            <image class="share-icon" src="/static/images/share/wechat.png" mode="aspectFit"></image>
            <text class="share-text">微信</text>
          </view>
          
          <view class="share-option" @click="shareToMoments">
            <image class="share-icon" src="/static/images/share/moments.png" mode="aspectFit"></image>
            <text class="share-text">朋友圈</text>
          </view>
          
          <view class="share-option" @click="copyTransactionLink">
            <image class="share-icon" src="/static/images/share/link.png" mode="aspectFit"></image>
            <text class="share-text">复制链接</text>
          </view>
          
          <view class="share-option" @click="saveReceipt">
            <image class="share-icon" src="/static/images/share/save.png" mode="aspectFit"></image>
            <text class="share-text">保存收据</text>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 收据弹窗 -->
    <uni-popup ref="receiptPopup" type="center">
      <view class="receipt-popup">
        <view class="receipt-header">
          <text class="receipt-title">交易收据</text>
          <uni-icons type="close" size="24" @click="closeReceiptPopup"></uni-icons>
        </view>

        <view class="receipt-content" id="receiptContent">
          <!-- 收据内容 -->
          <view class="receipt-info">
            <view class="receipt-logo">
              <image class="logo-img" src="/static/images/logo.png" mode="aspectFit"></image>
              <text class="logo-text">MEH钱包</text>
            </view>

            <view class="receipt-status">
              <text class="status-title">{{ getStatusText(transaction.status) }}</text>
            </view>

            <view class="receipt-amount">
              <text class="amount-label">{{ transaction.txType === 'SEND' ? '转出金额' : '转入金额' }}</text>
              <text class="amount-value">{{ transaction.txType === 'SEND' ? '-' : '+' }}{{ transaction.amount }} {{ transaction.coinType }}</text>
            </view>

            <view class="receipt-details">
              <view class="detail-row">
                <text class="detail-label">交易时间:</text>
                <text class="detail-value">{{ formatDateTime(transaction.createTime) }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">交易哈希:</text>
                <text class="detail-value">{{ formatHash(transaction.txHash) }}</text>
              </view>
              <view class="detail-row" v-if="transaction.gasFee">
                <text class="detail-label">网络费用:</text>
                <text class="detail-value">{{ transaction.gasFee }} {{ transaction.coinType }}</text>
              </view>
            </view>

            <view class="receipt-qr">
              <canvas canvas-id="receiptQR" class="qr-canvas"></canvas>
              <text class="qr-desc">扫码查看详情</text>
            </view>
          </view>
        </view>

        <view class="receipt-actions">
          <button class="receipt-btn save" @click="saveReceiptImage">保存图片</button>
          <button class="receipt-btn share" @click="shareReceipt">分享收据</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      transactionId: null,
      transaction: {
        id: null,
        txHash: '',
        txType: 'SEND',
        fromAddress: '',
        toAddress: '',
        amount: '0',
        coinType: 'ETH',
        status: 'PENDING',
        createTime: null,
        confirmations: 0,
        memo: '',
        gasFee: '',
        gasPrice: '',
        gasLimit: '',
        nonce: 0,
        blockNumber: null,
        blockHash: '',
        transactionIndex: null,
        contractAddress: '',
        inputData: '',
        fiatValue: '',
        gasFiatValue: '',
        tokenInfo: null
      },
      
      // 相关交易
      relatedTransactions: [],
      
      // 网络配置
      requiredConfirmations: 12,
      estimatedTime: 5,
      
      // 定时器
      refreshTimer: null
    }
  },
  
  computed: {
    // 进度百分比
    progressPercentage() {
      if (!this.transaction.confirmations || this.transaction.status !== 'PENDING') {
        return 0
      }
      return Math.min((this.transaction.confirmations / this.requiredConfirmations) * 100, 100)
    },
    
    // 是否可以重新发送
    canResend() {
      return this.transaction.status === 'FAILED' && this.transaction.txType === 'SEND'
    },
    
    // 是否可以加速
    canSpeedUp() {
      return this.transaction.status === 'PENDING' && this.transaction.txType === 'SEND'
    },
    
    // 是否可以取消
    canCancel() {
      return this.transaction.status === 'PENDING' && this.transaction.txType === 'SEND'
    }
  },
  
  onLoad(options) {
    if (options.txId) {
      this.transactionId = options.txId
      this.loadTransactionDetail()
    }
  },
  
  onShow() {
    // 启动定时刷新
    this.startRefreshTimer()
  },
  
  onHide() {
    // 停止定时刷新
    this.stopRefreshTimer()
  },
  
  onUnload() {
    this.stopRefreshTimer()
  },
  
  methods: {
    // 加载交易详情
    loadTransactionDetail() {
      uni.showLoading({ title: '加载中...' })
      
      this.$api.get(`/wallet-transactions/${this.transactionId}`).then(res => {
        this.transaction = res.data
        this.loadRelatedTransactions()
        this.updateEstimatedTime()
        uni.hideLoading()
      }).catch(err => {
        console.error('加载交易详情失败:', err)
        uni.hideLoading()
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      })
    },
    
    // 加载相关交易
    loadRelatedTransactions() {
      this.$api.get('/wallet-transactions/related', {
        params: {
          txId: this.transactionId,
          address: this.transaction.fromAddress
        }
      }).then(res => {
        this.relatedTransactions = res.data || []
      })
    },
    
    // 更新预计时间
    updateEstimatedTime() {
      if (this.transaction.status === 'PENDING') {
        const remainingConfirmations = Math.max(0, this.requiredConfirmations - (this.transaction.confirmations || 0))
        this.estimatedTime = Math.ceil(remainingConfirmations * 0.5) // 假设每个确认0.5分钟
      }
    },
    
    // 启动定时刷新
    startRefreshTimer() {
      if (this.transaction.status === 'PENDING') {
        this.refreshTimer = setInterval(() => {
          this.refreshTransactionStatus()
        }, 10000) // 每10秒刷新一次
      }
    },
    
    // 停止定时刷新
    stopRefreshTimer() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    
    // 刷新交易状态
    refreshTransactionStatus() {
      this.$api.get(`/wallet-transactions/${this.transactionId}/status`).then(res => {
        const newStatus = res.data
        if (newStatus.status !== this.transaction.status || 
            newStatus.confirmations !== this.transaction.confirmations) {
          this.transaction = { ...this.transaction, ...newStatus }
          this.updateEstimatedTime()
          
          // 如果交易完成，停止刷新
          if (newStatus.status !== 'PENDING') {
            this.stopRefreshTimer()
          }
        }
      }).catch(err => {
        console.error('刷新交易状态失败:', err)
      })
    },
    
    // 重新发送交易
    resendTransaction() {
      uni.showModal({
        title: '重新发送交易',
        content: '确定要重新发送这笔交易吗？',
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({
              url: `/pages/wallet/transfer?resendTxId=${this.transactionId}`
            })
          }
        }
      })
    },
    
    // 加速交易
    speedUpTransaction() {
      uni.showModal({
        title: '加速交易',
        content: '通过增加网络费用来加速交易确认，是否继续？',
        success: (res) => {
          if (res.confirm) {
            this.executeSpeedUp()
          }
        }
      })
    },
    
    // 执行加速
    executeSpeedUp() {
      uni.showLoading({ title: '加速中...' })
      
      this.$api.post(`/wallet-transactions/${this.transactionId}/speed-up`).then(res => {
        uni.hideLoading()
        uni.showToast({
          title: '加速成功',
          icon: 'success'
        })
        this.loadTransactionDetail()
      }).catch(err => {
        uni.hideLoading()
        uni.showToast({
          title: '加速失败',
          icon: 'error'
        })
      })
    },
    
    // 取消交易
    cancelTransaction() {
      uni.showModal({
        title: '取消交易',
        content: '取消交易需要发送一笔新的交易来覆盖原交易，是否继续？',
        success: (res) => {
          if (res.confirm) {
            this.executeCancel()
          }
        }
      })
    },
    
    // 执行取消
    executeCancel() {
      uni.showLoading({ title: '取消中...' })
      
      this.$api.post(`/wallet-transactions/${this.transactionId}/cancel`).then(res => {
        uni.hideLoading()
        uni.showToast({
          title: '取消成功',
          icon: 'success'
        })
        this.loadTransactionDetail()
      }).catch(err => {
        uni.hideLoading()
        uni.showToast({
          title: '取消失败',
          icon: 'error'
        })
      })
    },
    
    // 查看收据
    viewReceipt() {
      this.generateReceiptQR()
      this.$refs.receiptPopup.open()
    },
    
    // 生成收据二维码
    generateReceiptQR() {
      this.$nextTick(() => {
        const qrContent = `https://meh.business/tx/${this.transaction.txHash}`
        
        // 这里需要引入二维码生成库
        // QRCode.toCanvas(...)
      })
    },
    
    // 关闭收据弹窗
    closeReceiptPopup() {
      this.$refs.receiptPopup.close()
    },
    
    // 保存收据图片
    saveReceiptImage() {
      // 实现保存收据图片功能
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      })
    },
    
    // 分享收据
    shareReceipt() {
      this.closeReceiptPopup()
      this.shareTransaction()
    },
    
    // 分享交易
    shareTransaction() {
      this.$refs.sharePopup.open()
    },
    
    // 关闭分享弹窗
    closeSharePopup() {
      this.$refs.sharePopup.close()
    },
    
    // 分享到微信
    shareToWeChat() {
      const shareData = {
        title: `${this.getTxTypeName(this.transaction.txType)}详情`,
        desc: `${this.transaction.amount} ${this.transaction.coinType}`,
        link: `https://meh.business/tx/${this.transaction.txHash}`
      }
      
      // 调用微信分享
      console.log('分享到微信:', shareData)
      this.closeSharePopup()
    },
    
    // 分享到朋友圈
    shareToMoments() {
      // 实现朋友圈分享
      console.log('分享到朋友圈')
      this.closeSharePopup()
    },
    
    // 复制交易链接
    copyTransactionLink() {
      const link = `https://meh.business/tx/${this.transaction.txHash}`
      uni.setClipboardData({
        data: link,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success'
          })
        }
      })
      this.closeSharePopup()
    },
    
    // 保存收据
    saveReceipt() {
      this.closeSharePopup()
      this.viewReceipt()
    },
    
    // 举报交易
    reportTransaction() {
      uni.showModal({
        title: '举报交易',
        content: '请选择举报原因',
        success: (res) => {
          if (res.confirm) {
            // 打开举报页面
            uni.navigateTo({
              url: `/pages/wallet/report?txId=${this.transactionId}`
            })
          }
        }
      })
    },
    
    // 查看全部相关交易
    viewAllRelated() {
      uni.navigateTo({
        url: `/pages/wallet/transaction-history?address=${this.transaction.fromAddress}`
      })
    },
    
    // 打开其他交易
    openTransaction(txId) {
      uni.navigateTo({
        url: `/pages/wallet/transaction-detail?txId=${txId}`
      })
    },
    
    // 复制地址
    copyAddress(address) {
      uni.setClipboardData({
        data: address,
        success: () => {
          uni.showToast({
            title: '地址已复制',
            icon: 'success'
          })
        }
      })
    },
    
    // 查看地址
    viewAddress(address) {
      uni.navigateTo({
        url: `/pages/wallet/address-detail?address=${address}`
      })
    },
    
    // 查看合约
    viewContract(address) {
      uni.navigateTo({
        url: `/pages/wallet/contract-detail?address=${address}`
      })
    },
    
    // 复制哈希
    copyHash(hash) {
      uni.setClipboardData({
        data: hash,
        success: () => {
          uni.showToast({
            title: '哈希已复制',
            icon: 'success'
          })
        }
      })
    },
    
    // 复制数据
    copyData(data) {
      uni.setClipboardData({
        data: data,
        success: () => {
          uni.showToast({
            title: '数据已复制',
            icon: 'success'
          })
        }
      })
    },
    
    // 打开区块浏览器
    openBlockExplorer(hash) {
      const explorerUrl = this.getExplorerUrl(hash)
      
      // #ifdef H5
      window.open(explorerUrl, '_blank')
      // #endif
      
      // #ifndef H5
      uni.setClipboardData({
        data: explorerUrl,
        success: () => {
          uni.showModal({
            title: '提示',
            content: '浏览器链接已复制，请在浏览器中打开查看',
            showCancel: false
          })
        }
      })
      // #endif
    },
    
    // 获取浏览器URL
    getExplorerUrl(hash) {
      const explorerMap = {
        'ETH': 'https://etherscan.io/tx/',
        'BTC': 'https://blockchain.info/tx/',
        'BSC': 'https://bscscan.com/tx/'
      }
      
      const baseUrl = explorerMap[this.transaction.coinType] || explorerMap['ETH']
      return baseUrl + hash
    },
    
    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        'SUCCESS': 'checkmarkempty',
        'PENDING': 'spinner-cycle',
        'FAILED': 'closeempty'
      }
      return iconMap[status] || 'help'
    },
    
    // 获取状态颜色
    getStatusColor(status) {
      const colorMap = {
        'SUCCESS': '#4ECDC4',
        'PENDING': '#FFA726',
        'FAILED': '#FF6B6B'
      }
      return colorMap[status] || '#999'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        'SUCCESS': '交易成功',
        'PENDING': '待确认',
        'FAILED': '交易失败'
      }
      return textMap[status] || '未知状态'
    },
    
    // 获取状态描述
    getStatusDescription(status) {
      const descMap = {
        'SUCCESS': '交易已成功确认',
        'PENDING': '交易正在等待网络确认',
        'FAILED': '交易执行失败，请检查详情'
      }
      return descMap[status] || ''
    },
    
    // 获取交易类型名称
    getTxTypeName(type) {
      const typeMap = {
        'SEND': '转出',
        'RECEIVE': '转入',
        'APPROVE': '授权',
        'SWAP': '兑换'
      }
      return typeMap[type] || type
    },
    
    // 获取交易图标
    getTxIcon(type) {
      const iconMap = {
        'SEND': 'arrow-up',
        'RECEIVE': 'arrow-down',
        'APPROVE': 'checkmarkempty',
        'SWAP': 'loop'
      }
      return iconMap[type] || 'help'
    },
    
    // 获取交易图标颜色
    getTxIconColor(type) {
      const colorMap = {
        'SEND': '#FF6B6B',
        'RECEIVE': '#4ECDC4',
        'APPROVE': '#667eea',
        'SWAP': '#FFA726'
      }
      return colorMap[type] || '#999'
    },
    
    // 格式化地址
    formatAddress(address) {
      if (!address) return ''
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    },
    
    // 格式化哈希
    formatHash(hash) {
      if (!hash) return ''
      return `${hash.slice(0, 10)}...${hash.slice(-6)}`
    },
    
    // 格式化日期时间
    formatDateTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleString('zh-CN')
    },
    
    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    
    // 获取币种图标
    getCoinIcon(coinType) {
      return `/static/images/coins/${coinType.toLowerCase()}.png`
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss">
.detail-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 40rpx;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height);
  background: #fff;
  border-bottom: 1rpx solid #e0e0e0;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.status-card {
  margin: 24rpx 32rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 16rpx;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.success {
    background: rgba(76, 205, 196, 0.1);
  }
  
  &.pending {
    background: rgba(255, 167, 38, 0.1);
  }
  
  &.failed {
    background: rgba(255, 107, 107, 0.1);
  }
}

.status-info {
  flex: 1;
}

.status-text {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.status-desc {
  font-size: 24rpx;
  color: #666;
}

.amount-section {
  text-align: center;
  margin-bottom: 32rpx;
}

.amount-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.amount-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.amount-value {
  font-size: 48rpx;
  font-weight: bold;
  
  &.send {
    color: #FF6B6B;
  }
  
  &.receive {
    color: #4ECDC4;
  }
}

.amount-currency {
  font-size: 28rpx;
  color: #666;
}

.amount-fiat {
  font-size: 24rpx;
  color: #999;
}

.progress-section {
  padding-top: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.progress-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #333;
}

.progress-count {
  font-size: 24rpx;
  color: #667eea;
}

.progress-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.progress-fill {
  height: 100%;
  background: #667eea;
  transition: width 0.3s ease;
}

.progress-desc {
  font-size: 22rpx;
  color: #666;
  text-align: center;
}

.transaction-info {
  margin: 0 32rpx;
}

.info-section {
  margin-bottom: 24rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.info-list {
  // 样式
}

.info-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  font-size: 26rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  text-align: right;
  word-break: break-all;
}

.address-container,
.hash-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
}

.address-text,
.hash-text {
  flex: 1;
  font-size: 24rpx;
  color: #333;
  font-family: monospace;
  word-break: break-all;
}

.address-actions,
.hash-actions {
  display: flex;
  gap: 16rpx;
  flex-shrink: 0;
}

.fee-container {
  flex: 1;
  text-align: right;
}

.fee-value {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.fee-fiat {
  font-size: 22rpx;
  color: #666;
}

.token-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.token-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
}

.token-details {
  flex: 1;
}

.token-name {
  display: block;
  font-size: 24rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.token-symbol {
  font-size: 22rpx;
  color: #666;
}

.data-container {
  flex: 1;
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.data-text {
  flex: 1;
  min-height: 120rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #333;
  font-family: monospace;
  border: none;
  resize: none;
}

.action-section {
  margin: 24rpx 32rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.action-text {
  font-size: 22rpx;
  color: #333;
}

.related-section {
  margin: 0 32rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-action {
  font-size: 24rpx;
  color: #667eea;
}

.related-list {
  // 样式
}

.related-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.related-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
}

.related-info {
  flex: 1;
}

.related-type {
  display: block;
  font-size: 24rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.related-time {
  font-size: 22rpx;
  color: #666;
}

.related-amount {
  font-size: 24rpx;
  font-weight: bold;
  
  &.send {
    color: #FF6B6B;
  }
  
  &.receive {
    color: #4ECDC4;
  }
}

.share-popup {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.share-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.share-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.share-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 32rpx;
  padding: 32rpx;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.share-icon {
  width: 60rpx;
  height: 60rpx;
}

.share-text {
  font-size: 22rpx;
  color: #333;
}

.receipt-popup {
  width: 600rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 24rpx;
  max-height: 80vh;
}

.receipt-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.receipt-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.receipt-content {
  max-height: 60vh;
  overflow-y: auto;
}

.receipt-info {
  text-align: center;
}

.receipt-logo {
  margin-bottom: 32rpx;
}

.logo-img {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
}

.logo-text {
  display: block;
  font-size: 24rpx;
  color: #333;
}

.receipt-status {
  margin-bottom: 32rpx;
}

.status-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #4ECDC4;
}

.receipt-amount {
  margin-bottom: 32rpx;
}

.receipt-details {
  margin-bottom: 32rpx;
  text-align: left;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.detail-label {
  font-size: 22rpx;
  color: #666;
}

.detail-value {
  font-size: 22rpx;
  color: #333;
  font-family: monospace;
}

.receipt-qr {
  margin-bottom: 32rpx;
}

.qr-canvas {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 16rpx;
}

.qr-desc {
  font-size: 20rpx;
  color: #666;
}

.receipt-actions {
  display: flex;
  gap: 16rpx;
}

.receipt-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 26rpx;
  
  &.save {
    background: #f5f5f5;
    color: #666;
  }
  
  &.share {
    background: #667eea;
    color: #fff;
  }
}
</style>
