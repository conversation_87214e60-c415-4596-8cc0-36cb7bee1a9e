<template>
  <view class="history-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons type="left" size="24" color="#333" class="back-icon" @click="goBack"></uni-icons>
      <text class="nav-title">交易历史</text>
      <uni-icons type="search" size="24" color="#333" class="search-icon" @click="showSearch"></uni-icons>
    </view>

    <!-- 钱包选择器 -->
    <view class="wallet-selector" v-if="walletList.length > 1">
      <scroll-view class="wallet-scroll" scroll-x="true" show-scrollbar="false">
        <view 
          class="wallet-item" 
          v-for="wallet in walletList" 
          :key="wallet.id"
          :class="{ active: wallet.id === selectedWallet.id }"
          @click="switchWallet(wallet)"
        >
          <view class="wallet-icon">
            <image class="coin-icon" :src="getCoinIcon(wallet.coinType)" mode="aspectFit"></image>
          </view>
          <text class="wallet-name">{{ wallet.walletName }}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 搜索栏 -->
    <view class="search-section" v-if="showSearchBar">
      <view class="search-container">
        <uni-icons type="search" size="20" color="#999"></uni-icons>
        <input 
          class="search-input" 
          placeholder="搜索交易哈希或地址"
          v-model="searchKeyword"
          @input="onSearchInput"
        />
        <text class="search-cancel" @click="hideSearch">取消</text>
      </view>
    </view>

    <!-- 筛选栏 -->
    <view class="filter-section">
      <scroll-view class="filter-scroll" scroll-x="true" show-scrollbar="false">
        <view 
          class="filter-item" 
          v-for="filter in filterOptions" 
          :key="filter.value"
          :class="{ active: filter.value === selectedFilter }"
          @click="selectFilter(filter.value)"
        >
          <text class="filter-text">{{ filter.label }}</text>
        </view>
      </scroll-view>
      
      <view class="filter-actions">
        <uni-icons type="tune" size="20" color="#667eea" @click="showAdvancedFilter"></uni-icons>
      </view>
    </view>

    <!-- 交易统计 -->
    <view class="stats-section" v-if="transactionStats">
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-label">总交易数</text>
          <text class="stats-value">{{ transactionStats.totalCount }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">总转出</text>
          <text class="stats-value">{{ transactionStats.totalSent }} {{ selectedWallet.coinType }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">总转入</text>
          <text class="stats-value">{{ transactionStats.totalReceived }} {{ selectedWallet.coinType }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">手续费</text>
          <text class="stats-value">{{ transactionStats.totalFees }} {{ selectedWallet.coinType }}</text>
        </view>
      </view>
    </view>

    <!-- 交易列表 -->
    <view class="transactions-section">
      <!-- 按日期分组的交易 -->
      <view 
        class="date-group" 
        v-for="(group, date) in groupedTransactions" 
        :key="date"
      >
        <view class="date-header">
          <text class="date-text">{{ formatDate(date) }}</text>
          <text class="date-count">{{ group.length }}笔交易</text>
        </view>

        <view class="transaction-list">
          <view 
            class="transaction-item" 
            v-for="tx in group" 
            :key="tx.id"
            @click="openTransactionDetail(tx)"
          >
            <!-- 交易图标 -->
            <view class="tx-icon" :class="tx.txType.toLowerCase()">
              <uni-icons 
                :type="getTxIcon(tx.txType)" 
                size="20" 
                :color="getTxIconColor(tx.txType)"
              ></uni-icons>
            </view>

            <!-- 交易信息 -->
            <view class="tx-info">
              <view class="tx-header">
                <text class="tx-type">{{ getTxTypeName(tx.txType) }}</text>
                <view class="tx-status" :class="tx.status.toLowerCase()">
                  <view class="status-dot"></view>
                  <text class="status-text">{{ getStatusName(tx.status) }}</text>
                </view>
              </view>
              
              <view class="tx-details">
                <text class="tx-address">{{ getTxAddressLabel(tx) }}</text>
                <text class="tx-time">{{ formatTime(tx.createTime) }}</text>
              </view>

              <view class="tx-hash" v-if="tx.txHash">
                <text class="hash-text">{{ formatHash(tx.txHash) }}</text>
                <uni-icons type="copy" size="16" color="#999" @click.stop="copyHash(tx.txHash)"></uni-icons>
              </view>
            </view>

            <!-- 交易金额 -->
            <view class="tx-amount" :class="tx.txType.toLowerCase()">
              <text class="amount-value">
                {{ tx.txType === 'SEND' ? '-' : '+' }}{{ tx.amount }} {{ tx.coinType }}
              </text>
              <text class="amount-fiat" v-if="tx.fiatValue">
                ≈ ¥{{ tx.fiatValue }}
              </text>
              <text class="amount-fee" v-if="tx.txType === 'SEND' && tx.gasFee">
                手续费: {{ tx.gasFee }} {{ tx.coinType }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <view class="load-more-btn" @click="loadMoreTransactions" :class="{ loading: isLoading }">
          <uni-icons v-if="isLoading" type="spinner-cycle" size="20" color="#667eea"></uni-icons>
          <text class="load-text">{{ isLoading ? '加载中...' : '加载更多' }}</text>
        </view>
      </view>

      <!-- 空状态 -->
      <view class="empty-transactions" v-if="transactionList.length === 0 && !isLoading">
        <image class="empty-icon" src="/static/images/wallet/empty-transactions.png" mode="aspectFit"></image>
        <text class="empty-title">暂无交易记录</text>
        <text class="empty-desc">您还没有进行过任何交易</text>
        <button class="start-transfer-btn" @click="startTransfer">发起转账</button>
      </view>
    </view>

    <!-- 高级筛选弹窗 -->
    <uni-popup ref="advancedFilterPopup" type="bottom">
      <view class="advanced-filter">
        <view class="filter-header">
          <text class="filter-title">高级筛选</text>
          <uni-icons type="close" size="24" @click="closeAdvancedFilter"></uni-icons>
        </view>

        <view class="filter-content">
          <!-- 交易类型 -->
          <view class="filter-group">
            <text class="group-title">交易类型</text>
            <view class="filter-options">
              <view 
                class="filter-option" 
                v-for="type in transactionTypes" 
                :key="type.value"
                :class="{ active: advancedFilters.types.includes(type.value) }"
                @click="toggleTransactionType(type.value)"
              >
                <text class="option-text">{{ type.label }}</text>
              </view>
            </view>
          </view>

          <!-- 交易状态 -->
          <view class="filter-group">
            <text class="group-title">交易状态</text>
            <view class="filter-options">
              <view 
                class="filter-option" 
                v-for="status in transactionStatuses" 
                :key="status.value"
                :class="{ active: advancedFilters.statuses.includes(status.value) }"
                @click="toggleTransactionStatus(status.value)"
              >
                <text class="option-text">{{ status.label }}</text>
              </view>
            </view>
          </view>

          <!-- 金额范围 -->
          <view class="filter-group">
            <text class="group-title">金额范围</text>
            <view class="amount-range">
              <input 
                class="range-input" 
                type="digit" 
                placeholder="最小金额"
                v-model="advancedFilters.minAmount"
              />
              <text class="range-separator">-</text>
              <input 
                class="range-input" 
                type="digit" 
                placeholder="最大金额"
                v-model="advancedFilters.maxAmount"
              />
            </view>
          </view>

          <!-- 时间范围 -->
          <view class="filter-group">
            <text class="group-title">时间范围</text>
            <view class="date-range">
              <view class="date-picker" @click="selectStartDate">
                <text class="date-text">{{ advancedFilters.startDate || '开始日期' }}</text>
                <uni-icons type="calendar" size="16" color="#999"></uni-icons>
              </view>
              <text class="range-separator">-</text>
              <view class="date-picker" @click="selectEndDate">
                <text class="date-text">{{ advancedFilters.endDate || '结束日期' }}</text>
                <uni-icons type="calendar" size="16" color="#999"></uni-icons>
              </view>
            </view>
          </view>
        </view>

        <view class="filter-actions">
          <button class="reset-btn" @click="resetFilters">重置</button>
          <button class="apply-btn" @click="applyAdvancedFilters">应用筛选</button>
        </view>
      </view>
    </uni-popup>

    <!-- 导出选项弹窗 -->
    <uni-popup ref="exportPopup" type="bottom">
      <view class="export-popup">
        <view class="export-header">
          <text class="export-title">导出交易记录</text>
          <uni-icons type="close" size="24" @click="closeExportPopup"></uni-icons>
        </view>

        <view class="export-options">
          <view class="export-option" @click="exportAsCSV">
            <uni-icons type="document" size="24" color="#667eea"></uni-icons>
            <view class="option-info">
              <text class="option-title">CSV文件</text>
              <text class="option-desc">适合Excel打开</text>
            </view>
          </view>
          
          <view class="export-option" @click="exportAsPDF">
            <uni-icons type="document-filled" size="24" color="#FF6B6B"></uni-icons>
            <view class="option-info">
              <text class="option-title">PDF文件</text>
              <text class="option-desc">适合打印和查看</text>
            </view>
          </view>

          <view class="export-option" @click="shareTransactions">
            <uni-icons type="share" size="24" color="#4ECDC4"></uni-icons>
            <view class="option-info">
              <text class="option-title">分享记录</text>
              <text class="option-desc">生成可分享的链接</text>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 悬浮操作按钮 -->
    <view class="float-actions">
      <view class="float-btn export-btn" @click="showExportOptions">
        <uni-icons type="download" size="20" color="#fff"></uni-icons>
      </view>
      <view class="float-btn refresh-btn" @click="refreshTransactions">
        <uni-icons type="refresh" size="20" color="#fff"></uni-icons>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 钱包信息
      selectedWallet: {
        id: null,
        walletName: '',
        coinType: 'ETH'
      },
      walletList: [],
      
      // 交易数据
      transactionList: [],
      groupedTransactions: {},
      transactionStats: null,
      
      // 分页
      currentPage: 1,
      pageSize: 20,
      hasMore: true,
      isLoading: false,
      
      // 搜索和筛选
      showSearchBar: false,
      searchKeyword: '',
      selectedFilter: 'all',
      searchTimer: null,
      
      // 筛选选项
      filterOptions: [
        { label: '全部', value: 'all' },
        { label: '转入', value: 'receive' },
        { label: '转出', value: 'send' },
        { label: '失败', value: 'failed' },
        { label: '待确认', value: 'pending' }
      ],
      
      // 高级筛选
      advancedFilters: {
        types: [],
        statuses: [],
        minAmount: '',
        maxAmount: '',
        startDate: '',
        endDate: ''
      },
      
      // 交易类型选项
      transactionTypes: [
        { label: '转出', value: 'SEND' },
        { label: '转入', value: 'RECEIVE' },
        { label: '授权', value: 'APPROVE' },
        { label: '兑换', value: 'SWAP' }
      ],
      
      // 交易状态选项
      transactionStatuses: [
        { label: '成功', value: 'SUCCESS' },
        { label: '待确认', value: 'PENDING' },
        { label: '失败', value: 'FAILED' }
      ]
    }
  },
  
  onLoad(options) {
    if (options.walletId) {
      this.selectedWallet.id = parseInt(options.walletId)
    }
    if (options.type) {
      this.selectedFilter = options.type
    }
    
    this.loadWalletList()
    this.loadTransactionStats()
    this.loadTransactions(true)
  },
  
  onReachBottom() {
    if (this.hasMore && !this.isLoading) {
      this.loadMoreTransactions()
    }
  },
  
  onPullDownRefresh() {
    this.refreshTransactions()
  },
  
  methods: {
    // 加载钱包列表
    loadWalletList() {
      this.$api.get('/wallets').then(res => {
        this.walletList = res.data || []
        if (this.walletList.length > 0 && !this.selectedWallet.id) {
          this.selectedWallet = this.walletList[0]
        }
      })
    },
    
    // 加载交易统计
    loadTransactionStats() {
      if (!this.selectedWallet.id) return
      
      this.$api.get('/wallet-transactions/stats', {
        params: { walletId: this.selectedWallet.id }
      }).then(res => {
        this.transactionStats = res.data
      })
    },
    
    // 加载交易记录
    loadTransactions(reset = false) {
      if (!this.selectedWallet.id) return
      
      if (reset) {
        this.currentPage = 1
        this.transactionList = []
        this.hasMore = true
      }
      
      this.isLoading = true
      
      const params = {
        walletId: this.selectedWallet.id,
        page: this.currentPage,
        size: this.pageSize
      }
      
      // 添加筛选条件
      if (this.selectedFilter !== 'all') {
        if (this.selectedFilter === 'receive') params.txType = 'RECEIVE'
        if (this.selectedFilter === 'send') params.txType = 'SEND'
        if (this.selectedFilter === 'failed') params.status = 'FAILED'
        if (this.selectedFilter === 'pending') params.status = 'PENDING'
      }
      
      // 添加搜索条件
      if (this.searchKeyword) {
        params.keyword = this.searchKeyword
      }
      
      // 添加高级筛选条件
      if (this.advancedFilters.types.length > 0) {
        params.types = this.advancedFilters.types.join(',')
      }
      if (this.advancedFilters.statuses.length > 0) {
        params.statuses = this.advancedFilters.statuses.join(',')
      }
      if (this.advancedFilters.minAmount) {
        params.minAmount = this.advancedFilters.minAmount
      }
      if (this.advancedFilters.maxAmount) {
        params.maxAmount = this.advancedFilters.maxAmount
      }
      if (this.advancedFilters.startDate) {
        params.startDate = this.advancedFilters.startDate
      }
      if (this.advancedFilters.endDate) {
        params.endDate = this.advancedFilters.endDate
      }
      
      this.$api.get('/wallet-transactions/history', { params }).then(res => {
        const newTransactions = res.data.records || []
        
        if (reset) {
          this.transactionList = newTransactions
        } else {
          this.transactionList = [...this.transactionList, ...newTransactions]
        }
        
        this.hasMore = newTransactions.length === this.pageSize
        this.groupTransactionsByDate()
        
      }).catch(err => {
        console.error('加载交易记录失败:', err)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      }).finally(() => {
        this.isLoading = false
        if (reset) {
          uni.stopPullDownRefresh()
        }
      })
    },
    
    // 加载更多交易
    loadMoreTransactions() {
      this.currentPage++
      this.loadTransactions(false)
    },
    
    // 刷新交易记录
    refreshTransactions() {
      this.loadTransactionStats()
      this.loadTransactions(true)
    },
    
    // 按日期分组交易
    groupTransactionsByDate() {
      const grouped = {}
      
      this.transactionList.forEach(tx => {
        const date = new Date(tx.createTime).toDateString()
        if (!grouped[date]) {
          grouped[date] = []
        }
        grouped[date].push(tx)
      })
      
      this.groupedTransactions = grouped
    },
    
    // 切换钱包
    switchWallet(wallet) {
      this.selectedWallet = wallet
      this.loadTransactionStats()
      this.loadTransactions(true)
    },
    
    // 显示搜索栏
    showSearch() {
      this.showSearchBar = true
      this.$nextTick(() => {
        // 自动聚焦搜索输入框
      })
    },
    
    // 隐藏搜索栏
    hideSearch() {
      this.showSearchBar = false
      this.searchKeyword = ''
      this.loadTransactions(true)
    },
    
    // 搜索输入处理
    onSearchInput() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      
      this.searchTimer = setTimeout(() => {
        this.loadTransactions(true)
      }, 500)
    },
    
    // 选择筛选器
    selectFilter(filter) {
      this.selectedFilter = filter
      this.loadTransactions(true)
    },
    
    // 显示高级筛选
    showAdvancedFilter() {
      this.$refs.advancedFilterPopup.open()
    },
    
    // 关闭高级筛选
    closeAdvancedFilter() {
      this.$refs.advancedFilterPopup.close()
    },
    
    // 切换交易类型
    toggleTransactionType(type) {
      const index = this.advancedFilters.types.indexOf(type)
      if (index > -1) {
        this.advancedFilters.types.splice(index, 1)
      } else {
        this.advancedFilters.types.push(type)
      }
    },
    
    // 切换交易状态
    toggleTransactionStatus(status) {
      const index = this.advancedFilters.statuses.indexOf(status)
      if (index > -1) {
        this.advancedFilters.statuses.splice(index, 1)
      } else {
        this.advancedFilters.statuses.push(status)
      }
    },
    
    // 选择开始日期
    selectStartDate() {
      uni.showModal({
        title: '选择开始日期',
        editable: true,
        placeholderText: '请输入日期 (YYYY-MM-DD)',
        success: (res) => {
          if (res.confirm && res.content) {
            this.advancedFilters.startDate = res.content
          }
        }
      })
    },
    
    // 选择结束日期
    selectEndDate() {
      uni.showModal({
        title: '选择结束日期',
        editable: true,
        placeholderText: '请输入日期 (YYYY-MM-DD)',
        success: (res) => {
          if (res.confirm && res.content) {
            this.advancedFilters.endDate = res.content
          }
        }
      })
    },
    
    // 重置筛选
    resetFilters() {
      this.advancedFilters = {
        types: [],
        statuses: [],
        minAmount: '',
        maxAmount: '',
        startDate: '',
        endDate: ''
      }
    },
    
    // 应用高级筛选
    applyAdvancedFilters() {
      this.closeAdvancedFilter()
      this.loadTransactions(true)
    },
    
    // 显示导出选项
    showExportOptions() {
      this.$refs.exportPopup.open()
    },
    
    // 关闭导出弹窗
    closeExportPopup() {
      this.$refs.exportPopup.close()
    },
    
    // 导出为CSV
    exportAsCSV() {
      this.closeExportPopup()
      uni.showLoading({ title: '导出中...' })
      
      this.$api.post('/wallet-transactions/export/csv', {
        walletId: this.selectedWallet.id,
        filters: this.getExportFilters()
      }).then(res => {
        // 处理CSV下载
        uni.hideLoading()
        uni.showToast({
          title: '导出成功',
          icon: 'success'
        })
      }).catch(err => {
        uni.hideLoading()
        uni.showToast({
          title: '导出失败',
          icon: 'error'
        })
      })
    },
    
    // 导出为PDF
    exportAsPDF() {
      this.closeExportPopup()
      uni.showLoading({ title: '生成中...' })
      
      this.$api.post('/wallet-transactions/export/pdf', {
        walletId: this.selectedWallet.id,
        filters: this.getExportFilters()
      }).then(res => {
        // 处理PDF下载
        uni.hideLoading()
        uni.showToast({
          title: '生成成功',
          icon: 'success'
        })
      }).catch(err => {
        uni.hideLoading()
        uni.showToast({
          title: '生成失败',
          icon: 'error'
        })
      })
    },
    
    // 分享交易记录
    shareTransactions() {
      this.closeExportPopup()
      
      const shareData = {
        title: `${this.selectedWallet.walletName} 交易记录`,
        desc: `共${this.transactionList.length}笔交易`,
        link: `https://meh.business/transactions/${this.selectedWallet.id}`
      }
      
      // 调用分享功能
      console.log('分享交易记录:', shareData)
    },
    
    // 获取导出筛选条件
    getExportFilters() {
      return {
        selectedFilter: this.selectedFilter,
        searchKeyword: this.searchKeyword,
        advancedFilters: this.advancedFilters
      }
    },
    
    // 打开交易详情
    openTransactionDetail(tx) {
      uni.navigateTo({
        url: `/pages/wallet/transaction-detail?txId=${tx.id}`
      })
    },
    
    // 复制交易哈希
    copyHash(hash) {
      uni.setClipboardData({
        data: hash,
        success: () => {
          uni.showToast({
            title: '哈希已复制',
            icon: 'success'
          })
        }
      })
    },
    
    // 开始转账
    startTransfer() {
      uni.navigateTo({
        url: `/pages/wallet/transfer?walletId=${this.selectedWallet.id}`
      })
    },
    
    // 获取交易图标
    getTxIcon(type) {
      const iconMap = {
        'SEND': 'arrow-up',
        'RECEIVE': 'arrow-down',
        'APPROVE': 'checkmarkempty',
        'SWAP': 'loop'
      }
      return iconMap[type] || 'help'
    },
    
    // 获取交易图标颜色
    getTxIconColor(type) {
      const colorMap = {
        'SEND': '#FF6B6B',
        'RECEIVE': '#4ECDC4',
        'APPROVE': '#667eea',
        'SWAP': '#FFA726'
      }
      return colorMap[type] || '#999'
    },
    
    // 获取交易类型名称
    getTxTypeName(type) {
      const typeMap = {
        'SEND': '转出',
        'RECEIVE': '转入',
        'APPROVE': '授权',
        'SWAP': '兑换'
      }
      return typeMap[type] || type
    },
    
    // 获取状态名称
    getStatusName(status) {
      const statusMap = {
        'PENDING': '待确认',
        'SUCCESS': '成功',
        'FAILED': '失败'
      }
      return statusMap[status] || status
    },
    
    // 获取交易地址标签
    getTxAddressLabel(tx) {
      if (tx.txType === 'SEND') {
        return `转至: ${this.formatAddress(tx.toAddress)}`
      } else {
        return `来自: ${this.formatAddress(tx.fromAddress)}`
      }
    },
    
    // 格式化地址
    formatAddress(address) {
      if (!address) return ''
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    },
    
    // 格式化哈希
    formatHash(hash) {
      if (!hash) return ''
      return `${hash.slice(0, 10)}...${hash.slice(-6)}`
    },
    
    // 格式化日期
    formatDate(dateString) {
      const date = new Date(dateString)
      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)
      
      if (date.toDateString() === today.toDateString()) {
        return '今天'
      } else if (date.toDateString() === yesterday.toDateString()) {
        return '昨天'
      } else {
        return date.toLocaleDateString('zh-CN', {
          month: 'long',
          day: 'numeric'
        })
      }
    },
    
    // 格式化时间
    formatTime(time) {
      return new Date(time).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    
    // 获取币种图标
    getCoinIcon(coinType) {
      return `/static/images/coins/${coinType.toLowerCase()}.png`
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss">
.history-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height);
  background: #fff;
  border-bottom: 1rpx solid #e0e0e0;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.wallet-selector {
  margin: 24rpx 32rpx;
}

.wallet-scroll {
  white-space: nowrap;
}

.wallet-item {
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 24rpx;
  margin-right: 16rpx;
  background: #fff;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  
  &.active {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
  }
}

.wallet-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coin-icon {
  width: 32rpx;
  height: 32rpx;
}

.wallet-name {
  font-size: 26rpx;
  color: #333;
}

.search-section {
  margin: 0 32rpx 24rpx 32rpx;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: #fff;
  border-radius: 12rpx;
}

.search-input {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  border: none;
  background: transparent;
}

.search-cancel {
  font-size: 26rpx;
  color: #667eea;
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin: 0 32rpx 24rpx 32rpx;
}

.filter-scroll {
  flex: 1;
  white-space: nowrap;
}

.filter-item {
  display: inline-block;
  padding: 16rpx 24rpx;
  margin-right: 12rpx;
  background: #fff;
  border: 2rpx solid transparent;
  border-radius: 20rpx;
  
  &.active {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
  }
}

.filter-text {
  font-size: 24rpx;
  color: #333;
}

.filter-actions {
  padding: 16rpx;
  background: #fff;
  border-radius: 12rpx;
}

.stats-section {
  margin: 0 32rpx 24rpx 32rpx;
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.stats-item {
  text-align: center;
}

.stats-label {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.stats-value {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.transactions-section {
  margin: 0 32rpx;
}

.date-group {
  margin-bottom: 32rpx;
}

.date-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 24rpx;
  margin-bottom: 16rpx;
}

.date-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.date-count {
  font-size: 22rpx;
  color: #666;
}

.transaction-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.tx-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.send {
    background: rgba(255, 107, 107, 0.1);
  }
  
  &.receive {
    background: rgba(76, 205, 196, 0.1);
  }
  
  &.approve {
    background: rgba(102, 126, 234, 0.1);
  }
  
  &.swap {
    background: rgba(255, 167, 38, 0.1);
  }
}

.tx-info {
  flex: 1;
  margin-right: 16rpx;
}

.tx-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.tx-type {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.tx-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  
  &.success {
    background: rgba(76, 205, 196, 0.1);
    color: #4ECDC4;
  }
  
  &.pending {
    background: rgba(255, 167, 38, 0.1);
    color: #FFA726;
  }
  
  &.failed {
    background: rgba(255, 107, 107, 0.1);
    color: #FF6B6B;
  }
}

.status-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: currentColor;
}

.status-text {
  font-size: 20rpx;
}

.tx-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.tx-address {
  font-size: 24rpx;
  color: #666;
}

.tx-time {
  font-size: 22rpx;
  color: #999;
}

.tx-hash {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.hash-text {
  font-size: 22rpx;
  color: #999;
  font-family: monospace;
}

.tx-amount {
  text-align: right;
  
  &.send {
    color: #FF6B6B;
  }
  
  &.receive {
    color: #4ECDC4;
  }
}

.amount-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 4rpx;
}

.amount-fiat {
  display: block;
  font-size: 22rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.amount-fee {
  display: block;
  font-size: 20rpx;
  color: #999;
}

.load-more {
  margin-top: 32rpx;
  text-align: center;
}

.load-more-btn {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx 40rpx;
  background: #fff;
  border-radius: 12rpx;
  
  &.loading {
    opacity: 0.7;
  }
}

.load-text {
  font-size: 26rpx;
  color: #333;
}

.empty-transactions {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 32rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
}

.start-transfer-btn {
  padding: 20rpx 40rpx;
  background: #667eea;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
}

.advanced-filter {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.filter-content {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.filter-group {
  margin-bottom: 40rpx;
}

.group-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.filter-option {
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 20rpx;
  
  &.active {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
  }
}

.option-text {
  font-size: 24rpx;
  color: #333;
}

.amount-range,
.date-range {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.range-input {
  flex: 1;
  height: 80rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #333;
}

.range-separator {
  font-size: 24rpx;
  color: #666;
}

.date-picker {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.date-text {
  font-size: 26rpx;
  color: #333;
}

.filter-actions {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #e0e0e0;
}

.reset-btn,
.apply-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;
}

.apply-btn {
  background: #667eea;
  color: #fff;
}

.export-popup {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.export-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.export-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.export-options {
  padding: 32rpx;
}

.export-option {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.option-info {
  flex: 1;
}

.option-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.option-desc {
  font-size: 24rpx;
  color: #666;
}

.float-actions {
  position: fixed;
  bottom: 40rpx;
  right: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.float-btn {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.export-btn {
  background: #4ECDC4;
}

.refresh-btn {
  background: #667eea;
}
</style>
