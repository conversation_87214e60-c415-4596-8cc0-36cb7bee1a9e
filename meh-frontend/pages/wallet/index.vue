<template>
  <view class="wallet-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons type="left" size="24" color="#fff" class="back-icon" @click="goBack"></uni-icons>
      <text class="nav-title">我的钱包</text>
      <uni-icons type="gear" size="24" color="#fff" class="setting-icon" @click="openSettings"></uni-icons>
    </view>

    <!-- 钱包余额卡片 -->
    <view class="balance-card">
      <view class="balance-header">
        <view class="wallet-info">
          <text class="wallet-name">{{ currentWallet.walletName }}</text>
          <text class="wallet-address">{{ formatAddress(currentWallet.walletAddress) }}</text>
        </view>
        <view class="wallet-actions">
          <uni-icons type="eye" :class="balanceVisible ? 'visible' : 'hidden'" @click="toggleBalanceVisibility"></uni-icons>
          <uni-icons type="more-filled" size="20" color="#fff" @click="showWalletMenu"></uni-icons>
        </view>
      </view>
      
      <view class="balance-content">
        <view class="total-balance">
          <text class="balance-label">总资产 (USD)</text>
          <text class="balance-amount" v-if="balanceVisible">${{ totalBalance }}</text>
          <text class="balance-amount" v-else>****</text>
        </view>
        
        <view class="balance-change" :class="balanceChange.isPositive ? 'positive' : 'negative'">
          <text class="change-text">{{ balanceChange.isPositive ? '+' : '' }}{{ balanceChange.value }}%</text>
          <text class="change-label">24h</text>
        </view>
      </view>

      <!-- 快捷操作按钮 -->
      <view class="quick-actions">
        <view class="action-item" @click="openTransfer">
          <uni-icons type="arrow-up" size="24" color="#fff"></uni-icons>
          <text class="action-text">转账</text>
        </view>
        <view class="action-item" @click="openReceive">
          <uni-icons type="arrow-down" size="24" color="#fff"></uni-icons>
          <text class="action-text">收款</text>
        </view>
        <view class="action-item" @click="openScan">
          <uni-icons type="scan" size="24" color="#fff"></uni-icons>
          <text class="action-text">扫码</text>
        </view>
        <view class="action-item" @click="openSwap">
          <uni-icons type="loop" size="24" color="#fff"></uni-icons>
          <text class="action-text">兑换</text>
        </view>
      </view>
    </view>

    <!-- 钱包切换 -->
    <view class="wallet-switcher" v-if="walletList.length > 1">
      <scroll-view class="wallet-scroll" scroll-x="true" show-scrollbar="false">
        <view 
          class="wallet-item" 
          v-for="(wallet, index) in walletList" 
          :key="wallet.id"
          :class="{ active: wallet.id === currentWallet.id }"
          @click="switchWallet(wallet)"
        >
          <view class="wallet-icon">
            <image class="coin-icon" :src="getCoinIcon(wallet.coinType)" mode="aspectFit"></image>
          </view>
          <view class="wallet-info">
            <text class="wallet-name">{{ wallet.walletName }}</text>
            <text class="wallet-balance" v-if="balanceVisible">${{ wallet.balance || '0.00' }}</text>
            <text class="wallet-balance" v-else>****</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 资产列表 -->
    <view class="assets-section">
      <view class="section-header">
        <text class="section-title">我的资产</text>
        <view class="asset-actions">
          <text class="action-link" @click="openAddToken">添加代币</text>
          <text class="action-link" @click="openPortfolio">资产组合</text>
        </view>
      </view>

      <view class="asset-list">
        <view 
          class="asset-item" 
          v-for="(asset, index) in assetList" 
          :key="index"
          @click="openAssetDetail(asset)"
        >
          <view class="asset-icon">
            <image class="coin-icon" :src="getCoinIcon(asset.coinType)" mode="aspectFit"></image>
          </view>
          
          <view class="asset-info">
            <view class="asset-header">
              <text class="asset-name">{{ asset.name }}</text>
              <text class="asset-symbol">{{ asset.symbol }}</text>
            </view>
            <view class="asset-balance">
              <text class="balance-amount" v-if="balanceVisible">{{ asset.balance }}</text>
              <text class="balance-amount" v-else>****</text>
              <text class="balance-value" v-if="balanceVisible">${{ asset.value }}</text>
              <text class="balance-value" v-else>****</text>
            </view>
          </view>

          <view class="asset-change" :class="asset.change.isPositive ? 'positive' : 'negative'">
            <text class="change-value">{{ asset.change.isPositive ? '+' : '' }}{{ asset.change.value }}%</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 最近交易 -->
    <view class="transactions-section">
      <view class="section-header">
        <text class="section-title">最近交易</text>
        <text class="action-link" @click="openTransactionHistory">查看全部</text>
      </view>

      <view class="transaction-list">
        <view 
          class="transaction-item" 
          v-for="(tx, index) in recentTransactions" 
          :key="tx.id"
          @click="openTransactionDetail(tx)"
        >
          <view class="tx-icon" :class="tx.txType.toLowerCase()">
            <uni-icons 
              :type="tx.txType === 'SEND' ? 'arrow-up' : 'arrow-down'" 
              size="20" 
              :color="tx.txType === 'SEND' ? '#FF6B6B' : '#4ECDC4'"
            ></uni-icons>
          </view>

          <view class="tx-info">
            <view class="tx-header">
              <text class="tx-type">{{ getTxTypeName(tx.txType) }}</text>
              <text class="tx-address">{{ formatAddress(tx.txType === 'SEND' ? tx.toAddress : tx.fromAddress) }}</text>
            </view>
            <view class="tx-time">{{ formatTime(tx.createTime) }}</view>
          </view>

          <view class="tx-amount" :class="tx.txType.toLowerCase()">
            <text class="amount-value">{{ tx.txType === 'SEND' ? '-' : '+' }}{{ tx.amount }} {{ tx.coinType }}</text>
            <text class="amount-status" :class="tx.status.toLowerCase()">{{ getStatusName(tx.status) }}</text>
          </view>
        </view>

        <view class="empty-transactions" v-if="recentTransactions.length === 0">
          <image class="empty-icon" src="/static/images/wallet/empty-transactions.png" mode="aspectFit"></image>
          <text class="empty-text">暂无交易记录</text>
        </view>
      </view>
    </view>

    <!-- 钱包菜单弹窗 -->
    <uni-popup ref="walletMenuPopup" type="bottom">
      <view class="wallet-menu">
        <view class="menu-header">
          <text class="menu-title">钱包管理</text>
          <uni-icons type="close" size="24" @click="closeWalletMenu"></uni-icons>
        </view>
        
        <view class="menu-list">
          <view class="menu-item" @click="openWalletDetail">
            <uni-icons type="wallet" size="20" color="#666"></uni-icons>
            <text class="menu-text">钱包详情</text>
          </view>
          <view class="menu-item" @click="openAddressBook">
            <uni-icons type="contact" size="20" color="#666"></uni-icons>
            <text class="menu-text">地址簿</text>
          </view>
          <view class="menu-item" @click="openBackupWallet">
            <uni-icons type="locked" size="20" color="#666"></uni-icons>
            <text class="menu-text">备份钱包</text>
          </view>
          <view class="menu-item" @click="openSecuritySettings">
            <uni-icons type="gear" size="20" color="#666"></uni-icons>
            <text class="menu-text">安全设置</text>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 与名片关联提示 -->
    <uni-popup ref="cardBindPopup" type="center">
      <view class="card-bind-popup">
        <text class="popup-title">关联名片</text>
        <text class="popup-desc">将此钱包地址添加到您的电子名片中，方便他人向您转账</text>
        
        <view class="card-list">
          <view 
            class="card-item" 
            v-for="card in userCards" 
            :key="card.id"
            @click="bindToCard(card.id)"
          >
            <image class="card-avatar" :src="card.avatar" mode="aspectFill"></image>
            <view class="card-info">
              <text class="card-name">{{ card.name }}</text>
              <text class="card-position">{{ card.position }}</text>
            </view>
          </view>
        </view>

        <view class="popup-actions">
          <button class="cancel-btn" @click="closeCardBindPopup">取消</button>
          <button class="confirm-btn" @click="createNewCard">创建新名片</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 当前钱包信息
      currentWallet: {
        id: 1,
        walletName: '主钱包',
        walletAddress: '******************************************',
        coinType: 'ETH',
        balance: '1,234.56'
      },
      
      // 钱包列表
      walletList: [],
      
      // 总资产
      totalBalance: '12,345.67',
      
      // 余额变化
      balanceChange: {
        value: '2.34',
        isPositive: true
      },
      
      // 余额可见性
      balanceVisible: true,
      
      // 资产列表
      assetList: [
        {
          coinType: 'ETH',
          name: 'Ethereum',
          symbol: 'ETH',
          balance: '10.5432',
          value: '8,765.43',
          change: { value: '2.34', isPositive: true }
        },
        {
          coinType: 'USDT',
          name: 'Tether USD',
          symbol: 'USDT',
          balance: '2,500.00',
          value: '2,500.00',
          change: { value: '0.01', isPositive: true }
        }
      ],
      
      // 最近交易
      recentTransactions: [],
      
      // 用户名片
      userCards: []
    }
  },
  
  onLoad() {
    this.loadWalletData()
    this.loadUserCards()
  },
  
  onShow() {
    this.refreshWalletData()
  },
  
  methods: {
    // 加载钱包数据
    loadWalletData() {
      // 获取钱包列表
      this.$api.get('/wallets').then(res => {
        this.walletList = res.data || []
        if (this.walletList.length > 0 && !this.currentWallet.id) {
          this.currentWallet = this.walletList[0]
        }
      })
      
      // 获取最近交易
      this.loadRecentTransactions()
    },
    
    // 刷新钱包数据
    refreshWalletData() {
      if (this.currentWallet.id) {
        this.$api.post(`/wallets/${this.currentWallet.id}/refresh`).then(() => {
          this.loadWalletData()
        })
      }
    },
    
    // 加载最近交易
    loadRecentTransactions() {
      this.$api.get('/wallet-transactions/history', {
        params: {
          walletId: this.currentWallet.id,
          pageSize: 5
        }
      }).then(res => {
        this.recentTransactions = res.data.records || []
      })
    },
    
    // 加载用户名片
    loadUserCards() {
      this.$api.get('/cards').then(res => {
        this.userCards = res.data || []
      })
    },
    
    // 切换钱包
    switchWallet(wallet) {
      this.currentWallet = wallet
      this.loadRecentTransactions()
    },
    
    // 切换余额可见性
    toggleBalanceVisibility() {
      this.balanceVisible = !this.balanceVisible
    },
    
    // 格式化地址
    formatAddress(address) {
      if (!address) return ''
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    },
    
    // 格式化时间
    formatTime(time) {
      // 实现时间格式化逻辑
      return new Date(time).toLocaleDateString()
    },
    
    // 获取币种图标
    getCoinIcon(coinType) {
      return `/static/images/coins/${coinType.toLowerCase()}.png`
    },
    
    // 获取交易类型名称
    getTxTypeName(type) {
      const typeMap = {
        'SEND': '转出',
        'RECEIVE': '转入',
        'APPROVE': '授权'
      }
      return typeMap[type] || type
    },
    
    // 获取状态名称
    getStatusName(status) {
      const statusMap = {
        'PENDING': '待确认',
        'SUCCESS': '成功',
        'FAILED': '失败'
      }
      return statusMap[status] || status
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },
    
    // 打开设置
    openSettings() {
      uni.navigateTo({
        url: '/pages/wallet/settings'
      })
    },
    
    // 显示钱包菜单
    showWalletMenu() {
      this.$refs.walletMenuPopup.open()
    },
    
    // 关闭钱包菜单
    closeWalletMenu() {
      this.$refs.walletMenuPopup.close()
    },
    
    // 打开转账
    openTransfer() {
      uni.navigateTo({
        url: `/pages/wallet/transfer?walletId=${this.currentWallet.id}`
      })
    },
    
    // 打开收款
    openReceive() {
      uni.navigateTo({
        url: `/pages/wallet/receive?walletId=${this.currentWallet.id}`
      })
    },
    
    // 打开扫码
    openScan() {
      uni.scanCode({
        success: (res) => {
          // 处理扫码结果
          this.handleScanResult(res.result)
        }
      })
    },
    
    // 处理扫码结果
    handleScanResult(result) {
      // 解析二维码内容，跳转到相应页面
      console.log('扫码结果:', result)
    },
    
    // 打开兑换
    openSwap() {
      uni.navigateTo({
        url: '/pages/wallet/swap'
      })
    },
    
    // 打开资产组合
    openPortfolio() {
      uni.navigateTo({
        url: `/pages/wallet/portfolio?walletId=${this.currentWallet.id}`
      })
    },
    
    // 打开添加代币
    openAddToken() {
      uni.navigateTo({
        url: '/pages/wallet/add-token'
      })
    },
    
    // 打开资产详情
    openAssetDetail(asset) {
      uni.navigateTo({
        url: `/pages/wallet/asset-detail?coinType=${asset.coinType}&walletId=${this.currentWallet.id}`
      })
    },
    
    // 打开交易历史
    openTransactionHistory() {
      uni.navigateTo({
        url: `/pages/wallet/transaction-history?walletId=${this.currentWallet.id}`
      })
    },
    
    // 打开交易详情
    openTransactionDetail(tx) {
      uni.navigateTo({
        url: `/pages/wallet/transaction-detail?txId=${tx.id}`
      })
    },
    
    // 打开钱包详情
    openWalletDetail() {
      this.closeWalletMenu()
      uni.navigateTo({
        url: `/pages/wallet/detail?walletId=${this.currentWallet.id}`
      })
    },
    
    // 打开地址簿
    openAddressBook() {
      this.closeWalletMenu()
      uni.navigateTo({
        url: '/pages/wallet/address-book'
      })
    },
    
    // 打开备份钱包
    openBackupWallet() {
      this.closeWalletMenu()
      uni.navigateTo({
        url: `/pages/wallet/backup?walletId=${this.currentWallet.id}`
      })
    },
    
    // 打开安全设置
    openSecuritySettings() {
      this.closeWalletMenu()
      uni.navigateTo({
        url: `/pages/wallet/security?walletId=${this.currentWallet.id}`
      })
    },
    
    // 关联到名片
    bindToCard(cardId) {
      this.$api.post(`/wallets/${this.currentWallet.id}/bind-card`, {
        cardId: cardId
      }).then(() => {
        uni.showToast({
          title: '关联成功',
          icon: 'success'
        })
        this.closeCardBindPopup()
      })
    },
    
    // 创建新名片
    createNewCard() {
      this.closeCardBindPopup()
      uni.navigateTo({
        url: `/pages/card/create?walletAddress=${this.currentWallet.walletAddress}`
      })
    },
    
    // 关闭名片关联弹窗
    closeCardBindPopup() {
      this.$refs.cardBindPopup.close()
    }
  }
}
</script>

<style lang="scss">
.wallet-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height);
  background: transparent;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
}

.balance-card {
  margin: 0 32rpx 32rpx 32rpx;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  backdrop-filter: blur(10px);
}

.balance-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.wallet-info {
  flex: 1;
}

.wallet-name {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8rpx;
}

.wallet-address {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
}

.wallet-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.balance-content {
  margin-bottom: 40rpx;
}

.balance-label {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 8rpx;
}

.balance-amount {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 16rpx;
}

.balance-change {
  display: flex;
  align-items: center;
  gap: 8rpx;
  
  &.positive {
    color: #4ECDC4;
  }
  
  &.negative {
    color: #FF6B6B;
  }
}

.quick-actions {
  display: flex;
  justify-content: space-between;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
}

.action-text {
  font-size: 24rpx;
  color: #fff;
}

.wallet-switcher {
  margin: 0 32rpx 32rpx 32rpx;
}

.wallet-scroll {
  white-space: nowrap;
}

.wallet-item {
  display: inline-flex;
  align-items: center;
  padding: 24rpx;
  margin-right: 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  min-width: 200rpx;
  
  &.active {
    background: rgba(255, 255, 255, 0.2);
  }
}

.assets-section,
.transactions-section {
  margin: 0 32rpx 32rpx 32rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 24rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.action-link {
  font-size: 24rpx;
  color: #667eea;
}

.asset-item,
.transaction-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.asset-icon,
.tx-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coin-icon {
  width: 60rpx;
  height: 60rpx;
}

.asset-info,
.tx-info {
  flex: 1;
}

.asset-header,
.tx-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 8rpx;
}

.asset-name,
.tx-type {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.asset-symbol,
.tx-address {
  font-size: 24rpx;
  color: #999;
}

.asset-balance,
.tx-time {
  font-size: 24rpx;
  color: #666;
}

.asset-change,
.tx-amount {
  text-align: right;
}

.wallet-menu {
  padding: 32rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  gap: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.card-bind-popup {
  width: 600rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 24rpx;
}

.popup-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16rpx;
}

.popup-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-bottom: 32rpx;
}

.card-list {
  margin-bottom: 32rpx;
}

.card-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
}

.card-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.popup-actions {
  display: flex;
  gap: 16rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #667eea;
  color: #fff;
}

.empty-transactions {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
