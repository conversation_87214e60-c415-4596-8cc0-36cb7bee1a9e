<template>
  <view class="create-wallet-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons type="left" size="24" color="#333" class="back-icon" @click="goBack"></uni-icons>
      <text class="nav-title">创建钱包</text>
    </view>

    <!-- 创建方式选择 -->
    <view class="create-methods" v-if="currentStep === 1">
      <view class="method-card" @click="selectMethod('create')">
        <view class="method-icon">
          <uni-icons type="plus-filled" size="48" color="#667eea"></uni-icons>
        </view>
        <view class="method-info">
          <text class="method-title">创建新钱包</text>
          <text class="method-desc">生成全新的助记词和钱包地址</text>
        </view>
        <uni-icons type="right" size="20" color="#999"></uni-icons>
      </view>

      <view class="method-card" @click="selectMethod('import')">
        <view class="method-icon">
          <uni-icons type="download-filled" size="48" color="#4ECDC4"></uni-icons>
        </view>
        <view class="method-info">
          <text class="method-title">导入钱包</text>
          <text class="method-desc">通过助记词或私钥导入现有钱包</text>
        </view>
        <uni-icons type="right" size="20" color="#999"></uni-icons>
      </view>

      <view class="method-card" @click="selectMethod('hardware')">
        <view class="method-icon">
          <uni-icons type="gear-filled" size="48" color="#FF6B6B"></uni-icons>
        </view>
        <view class="method-info">
          <text class="method-title">硬件钱包</text>
          <text class="method-desc">连接硬件钱包设备</text>
        </view>
        <uni-icons type="right" size="20" color="#999"></uni-icons>
      </view>

      <!-- 与名片关联提示 -->
      <view class="integration-tip">
        <view class="tip-icon">
          <uni-icons type="info-filled" size="24" color="#667eea"></uni-icons>
        </view>
        <view class="tip-content">
          <text class="tip-title">与名片无缝集成</text>
          <text class="tip-desc">创建后可将钱包地址添加到您的电子名片中，方便商务伙伴向您转账</text>
        </view>
      </view>
    </view>

    <!-- 创建新钱包流程 -->
    <view class="create-flow" v-if="selectedMethod === 'create'">
      <!-- 步骤2: 设置钱包信息 -->
      <view class="step-content" v-if="currentStep === 2">
        <view class="step-header">
          <text class="step-title">设置钱包信息</text>
          <text class="step-desc">为您的钱包设置名称和密码</text>
        </view>

        <view class="form-section">
          <view class="form-item">
            <text class="form-label">钱包名称</text>
            <input 
              class="form-input" 
              v-model="walletForm.walletName" 
              placeholder="请输入钱包名称"
              maxlength="20"
            />
          </view>

          <view class="form-item">
            <text class="form-label">选择币种</text>
            <view class="coin-selector">
              <view 
                class="coin-item"
                v-for="coin in supportedCoins"
                :key="coin.type"
                :class="{ active: walletForm.coinType === coin.type }"
                @click="selectCoin(coin.type)"
              >
                <image class="coin-icon" :src="coin.icon" mode="aspectFit"></image>
                <text class="coin-name">{{ coin.name }}</text>
                <text class="coin-symbol">{{ coin.symbol }}</text>
              </view>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">钱包密码</text>
            <input 
              class="form-input" 
              v-model="walletForm.password" 
              placeholder="请设置钱包密码"
              password
              maxlength="20"
            />
            <text class="form-tip">密码用于加密保护您的钱包，请牢记</text>
          </view>

          <view class="form-item">
            <text class="form-label">确认密码</text>
            <input 
              class="form-input" 
              v-model="confirmPassword" 
              placeholder="请再次输入密码"
              password
              maxlength="20"
            />
          </view>

          <view class="form-item checkbox-item">
            <checkbox 
              :checked="agreedTerms" 
              @change="handleAgreeChange"
              color="#667eea"
            />
            <text class="checkbox-text">
              我已阅读并同意
              <text class="link-text" @click="openTerms">《用户协议》</text>
              和
              <text class="link-text" @click="openPrivacy">《隐私政策》</text>
            </text>
          </view>
        </view>
      </view>

      <!-- 步骤3: 生成助记词 -->
      <view class="step-content" v-if="currentStep === 3">
        <view class="step-header">
          <text class="step-title">备份助记词</text>
          <text class="step-desc">请按顺序记录下助记词，用于恢复钱包</text>
        </view>

        <view class="mnemonic-warning">
          <uni-icons type="warn" size="24" color="#FF6B6B"></uni-icons>
          <text class="warning-text">请将助记词抄写在纸上并安全保存，任何人获得助记词都可以控制您的钱包</text>
        </view>

        <view class="mnemonic-container">
          <view 
            class="mnemonic-word"
            v-for="(word, index) in mnemonicWords"
            :key="index"
          >
            <text class="word-index">{{ index + 1 }}</text>
            <text class="word-text">{{ word }}</text>
          </view>
        </view>

        <view class="mnemonic-actions">
          <button class="copy-btn" @click="copyMnemonic">
            <uni-icons type="copy" size="20" color="#667eea"></uni-icons>
            <text>复制助记词</text>
          </button>
          <button class="screenshot-btn" @click="showScreenshotWarning">
            <uni-icons type="camera" size="20" color="#FF6B6B"></uni-icons>
            <text>截图保存</text>
          </button>
        </view>

        <view class="backup-checkbox">
          <checkbox 
            :checked="mnemonicBackedUp" 
            @change="handleBackupChange"
            color="#667eea"
          />
          <text class="checkbox-text">我已安全备份助记词</text>
        </view>
      </view>

      <!-- 步骤4: 验证助记词 -->
      <view class="step-content" v-if="currentStep === 4">
        <view class="step-header">
          <text class="step-title">验证助记词</text>
          <text class="step-desc">请按顺序点击助记词以验证您已正确备份</text>
        </view>

        <view class="verify-container">
          <view class="selected-words">
            <view 
              class="selected-word"
              v-for="(word, index) in selectedWords"
              :key="index"
              @click="removeWord(index)"
            >
              <text class="word-index">{{ getWordOriginalIndex(word) + 1 }}</text>
              <text class="word-text">{{ word }}</text>
            </view>
          </view>

          <view class="word-options">
            <view 
              class="word-option"
              v-for="(word, index) in shuffledWords"
              :key="index"
              :class="{ disabled: selectedWords.includes(word) }"
              @click="selectWord(word)"
            >
              <text class="word-text">{{ word }}</text>
            </view>
          </view>
        </view>

        <view class="verify-result" v-if="verifyError">
          <uni-icons type="close-filled" size="20" color="#FF6B6B"></uni-icons>
          <text class="error-text">助记词顺序不正确，请重新选择</text>
        </view>
      </view>
    </view>

    <!-- 导入钱包流程 -->
    <view class="import-flow" v-if="selectedMethod === 'import'">
      <view class="step-content" v-if="currentStep === 2">
        <view class="step-header">
          <text class="step-title">导入钱包</text>
          <text class="step-desc">通过助记词或私钥导入现有钱包</text>
        </view>

        <view class="import-methods">
          <view class="import-tab">
            <view 
              class="tab-item"
              :class="{ active: importType === 'mnemonic' }"
              @click="importType = 'mnemonic'"
            >
              <text>助记词</text>
            </view>
            <view 
              class="tab-item"
              :class="{ active: importType === 'privateKey' }"
              @click="importType = 'privateKey'"
            >
              <text>私钥</text>
            </view>
          </view>

          <view class="import-form">
            <!-- 助记词导入 -->
            <view v-if="importType === 'mnemonic'">
              <textarea 
                class="import-textarea"
                v-model="importForm.mnemonic"
                placeholder="请输入12或24个助记词，用空格分隔"
                maxlength="300"
              ></textarea>
              <text class="import-tip">请确保助记词顺序正确</text>
            </view>

            <!-- 私钥导入 -->
            <view v-if="importType === 'privateKey'">
              <textarea 
                class="import-textarea"
                v-model="importForm.privateKey"
                placeholder="请输入私钥"
                maxlength="100"
              ></textarea>
              <text class="import-tip">私钥是64位十六进制字符串</text>
            </view>

            <view class="form-item">
              <text class="form-label">钱包名称</text>
              <input 
                class="form-input" 
                v-model="importForm.walletName" 
                placeholder="请输入钱包名称"
                maxlength="20"
              />
            </view>

            <view class="form-item">
              <text class="form-label">钱包密码</text>
              <input 
                class="form-input" 
                v-model="importForm.password" 
                placeholder="请设置钱包密码"
                password
                maxlength="20"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 硬件钱包流程 -->
    <view class="hardware-flow" v-if="selectedMethod === 'hardware'">
      <view class="step-content" v-if="currentStep === 2">
        <view class="step-header">
          <text class="step-title">连接硬件钱包</text>
          <text class="step-desc">请确保您的硬件钱包已连接并解锁</text>
        </view>

        <view class="hardware-list">
          <view class="hardware-item" @click="connectHardware('ledger')">
            <image class="hardware-icon" src="/static/images/wallet/ledger.png" mode="aspectFit"></image>
            <view class="hardware-info">
              <text class="hardware-name">Ledger</text>
              <text class="hardware-desc">支持Ledger Nano S/X</text>
            </view>
            <text class="hardware-status">未连接</text>
          </view>

          <view class="hardware-item" @click="connectHardware('trezor')">
            <image class="hardware-icon" src="/static/images/wallet/trezor.png" mode="aspectFit"></image>
            <view class="hardware-info">
              <text class="hardware-name">Trezor</text>
              <text class="hardware-desc">支持Trezor One/Model T</text>
            </view>
            <text class="hardware-status">未连接</text>
          </view>
        </view>

        <view class="hardware-tips">
          <text class="tip-title">连接说明：</text>
          <text class="tip-item">1. 确保硬件钱包已通过USB连接到设备</text>
          <text class="tip-item">2. 在硬件钱包上输入PIN码解锁</text>
          <text class="tip-item">3. 打开对应的应用程序（如Ethereum App）</text>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-actions">
      <button 
        class="prev-btn" 
        v-if="currentStep > 1" 
        @click="prevStep"
      >
        上一步
      </button>
      
      <button 
        class="next-btn"
        :class="{ disabled: !canNext }"
        @click="nextStep"
        :disabled="!canNext"
      >
        {{ getNextButtonText() }}
      </button>
    </view>

    <!-- 截图警告弹窗 -->
    <uni-popup ref="screenshotWarning" type="center">
      <view class="warning-popup">
        <uni-icons type="warn" size="48" color="#FF6B6B"></uni-icons>
        <text class="warning-title">安全警告</text>
        <text class="warning-desc">截图保存助记词存在安全风险，建议手写记录在纸上</text>
        <view class="warning-actions">
          <button class="cancel-btn" @click="closeScreenshotWarning">取消</button>
          <button class="confirm-btn" @click="confirmScreenshot">仍要截图</button>
        </view>
      </view>
    </uni-popup>

    <!-- 加载弹窗 -->
    <uni-popup ref="loadingPopup" type="center">
      <view class="loading-popup">
        <uni-icons type="spinner-cycle" size="48" color="#667eea" class="loading-icon"></uni-icons>
        <text class="loading-text">{{ loadingText }}</text>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentStep: 1,
      selectedMethod: '',
      
      // 钱包表单
      walletForm: {
        walletName: '',
        coinType: 'MATIC', // 默认选择Polygon原生币
        password: ''
      },
      confirmPassword: '',
      agreedTerms: false,
      
      // 助记词相关
      mnemonicWords: [],
      mnemonicBackedUp: false,
      selectedWords: [],
      shuffledWords: [],
      verifyError: false,
      
      // 导入表单
      importType: 'mnemonic',
      importForm: {
        mnemonic: '',
        privateKey: '',
        walletName: '',
        password: ''
      },
      
      // 支持的币种（Polygon网络）
      supportedCoins: [
        {
          type: 'MATIC',
          name: 'Polygon',
          symbol: 'MATIC',
          icon: '/static/images/coins/matic.png',
          badge: '原生币',
          desc: 'Polygon网络原生代币，用于支付Gas费用'
        },
        {
          type: 'MBC',
          name: 'MEH Business Card Token',
          symbol: 'MBC',
          icon: '/static/images/coins/mbc.png',
          badge: '项目币',
          desc: 'MEH电子名片项目专用Token，用于积分奖励'
        },
        {
          type: 'USDT',
          name: 'Tether USD (Polygon)',
          symbol: 'USDT',
          icon: '/static/images/coins/usdt.png',
          desc: '在Polygon网络上的稳定币USDT'
        },
        {
          type: 'USDC',
          name: 'USD Coin (Polygon)',
          symbol: 'USDC',
          icon: '/static/images/coins/usdc.png',
          desc: '在Polygon网络上的稳定币USDC'
        },
        {
          type: 'ETH',
          name: 'Ethereum (Polygon)',
          symbol: 'ETH',
          icon: '/static/images/coins/eth.png',
          desc: '在Polygon网络上的桥接ETH'
        }
      ],
      
      loadingText: ''
    }
  },
  
  computed: {
    canNext() {
      switch (this.currentStep) {
        case 1:
          return this.selectedMethod !== ''
        case 2:
          if (this.selectedMethod === 'create') {
            return this.walletForm.walletName && 
                   this.walletForm.password && 
                   this.confirmPassword === this.walletForm.password &&
                   this.agreedTerms
          } else if (this.selectedMethod === 'import') {
            return this.importForm.walletName &&
                   this.importForm.password &&
                   (this.importForm.mnemonic || this.importForm.privateKey)
          }
          return true
        case 3:
          return this.mnemonicBackedUp
        case 4:
          return this.selectedWords.length === this.mnemonicWords.length
        default:
          return true
      }
    }
  },
  
  methods: {
    // 选择创建方式
    selectMethod(method) {
      this.selectedMethod = method
      this.currentStep = 2
      
      if (method === 'create') {
        this.generateMnemonic()
      }
    },
    
    // 选择币种
    selectCoin(coinType) {
      this.walletForm.coinType = coinType
    },
    
    // 生成助记词
    generateMnemonic() {
      this.$api.post('/wallets/generate-mnemonic').then(res => {
        this.mnemonicWords = res.data.words
        this.shuffledWords = [...this.mnemonicWords].sort(() => Math.random() - 0.5)
      })
    },
    
    // 复制助记词
    copyMnemonic() {
      uni.setClipboardData({
        data: this.mnemonicWords.join(' '),
        success: () => {
          uni.showToast({
            title: '已复制',
            icon: 'success'
          })
        }
      })
    },
    
    // 显示截图警告
    showScreenshotWarning() {
      this.$refs.screenshotWarning.open()
    },
    
    // 关闭截图警告
    closeScreenshotWarning() {
      this.$refs.screenshotWarning.close()
    },
    
    // 确认截图
    confirmScreenshot() {
      this.closeScreenshotWarning()
      // 这里可以触发截图功能或跳转到系统截图
      uni.showToast({
        title: '请注意安全',
        icon: 'none'
      })
    },
    
    // 选择验证词
    selectWord(word) {
      if (!this.selectedWords.includes(word)) {
        this.selectedWords.push(word)
        this.verifyError = false
      }
    },
    
    // 移除选择的词
    removeWord(index) {
      this.selectedWords.splice(index, 1)
      this.verifyError = false
    },
    
    // 获取词的原始索引
    getWordOriginalIndex(word) {
      return this.mnemonicWords.indexOf(word)
    },
    
    // 连接硬件钱包
    connectHardware(type) {
      this.loadingText = '正在连接硬件钱包...'
      this.$refs.loadingPopup.open()
      
      // 模拟连接过程
      setTimeout(() => {
        this.$refs.loadingPopup.close()
        uni.showToast({
          title: '连接失败',
          icon: 'none'
        })
      }, 3000)
    },
    
    // 上一步
    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep--
      }
    },
    
    // 下一步
    nextStep() {
      if (!this.canNext) return
      
      if (this.selectedMethod === 'create') {
        if (this.currentStep === 4) {
          // 验证助记词
          if (this.verifyMnemonic()) {
            this.createWallet()
          } else {
            this.verifyError = true
          }
        } else {
          this.currentStep++
        }
      } else if (this.selectedMethod === 'import') {
        this.importWallet()
      } else if (this.selectedMethod === 'hardware') {
        // 硬件钱包连接逻辑
      }
    },
    
    // 验证助记词
    verifyMnemonic() {
      return JSON.stringify(this.selectedWords) === JSON.stringify(this.mnemonicWords)
    },
    
    // 创建钱包
    createWallet() {
      this.loadingText = '正在创建钱包...'
      this.$refs.loadingPopup.open()
      
      const data = {
        walletName: this.walletForm.walletName,
        walletType: 'HD',
        coinType: this.walletForm.coinType,
        networkType: 'POLYGON_TESTNET', // 使用Polygon测试网
        mnemonic: this.mnemonicWords.join(' '),
        password: this.walletForm.password,
        isMain: true
      }
      
      this.$api.post('/wallets', data).then(res => {
        this.$refs.loadingPopup.close()
        
        uni.showToast({
          title: '钱包创建成功',
          icon: 'success'
        })
        
        // 跳转到钱包主页
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/wallet/index'
          })
        }, 1500)
      }).catch(() => {
        this.$refs.loadingPopup.close()
      })
    },
    
    // 导入钱包
    importWallet() {
      this.loadingText = '正在导入钱包...'
      this.$refs.loadingPopup.open()
      
      const data = {
        walletName: this.importForm.walletName,
        walletType: 'IMPORT',
        coinType: 'MATIC', // 默认导入MATIC钱包
        networkType: 'POLYGON_TESTNET', // 使用Polygon测试网
        [this.importType]: this.importForm[this.importType],
        password: this.importForm.password
      }
      
      this.$api.post('/wallets', data).then(res => {
        this.$refs.loadingPopup.close()
        
        uni.showToast({
          title: '钱包导入成功',
          icon: 'success'
        })
        
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/wallet/index'
          })
        }, 1500)
      }).catch(() => {
        this.$refs.loadingPopup.close()
      })
    },
    
    // 获取下一步按钮文本
    getNextButtonText() {
      if (this.selectedMethod === 'create') {
        switch (this.currentStep) {
          case 1: return '选择方式'
          case 2: return '生成助记词'
          case 3: return '验证助记词'
          case 4: return '创建钱包'
          default: return '下一步'
        }
      } else if (this.selectedMethod === 'import') {
        return '导入钱包'
      } else if (this.selectedMethod === 'hardware') {
        return '连接钱包'
      }
      return '下一步'
    },
    
    // 处理同意协议
    handleAgreeChange(e) {
      this.agreedTerms = e.detail.value.length > 0
    },
    
    // 处理备份确认
    handleBackupChange(e) {
      this.mnemonicBackedUp = e.detail.value.length > 0
    },
    
    // 打开条款
    openTerms() {
      uni.navigateTo({
        url: '/pages/agreement/user'
      })
    },
    
    // 打开隐私政策
    openPrivacy() {
      uni.navigateTo({
        url: '/pages/agreement/privacy'
      })
    },
    
    // 返回
    goBack() {
      if (this.currentStep > 1) {
        this.currentStep--
      } else {
        uni.navigateBack()
      }
    }
  }
}
</script>

<style lang="scss">
.create-wallet-container {
  min-height: 100vh;
  background: #f5f6f7;
}

.nav-bar {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #e0e0e0;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.create-methods {
  padding: 32rpx;
}

.method-card {
  display: flex;
  align-items: center;
  padding: 32rpx;
  margin-bottom: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.method-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f8f9fa;
}

.method-info {
  flex: 1;
}

.method-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.method-desc {
  font-size: 24rpx;
  color: #666;
}

.integration-tip {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  background: #e8f2ff;
  border-radius: 12rpx;
  margin-top: 32rpx;
}

.tip-icon {
  margin-right: 16rpx;
  margin-top: 4rpx;
}

.tip-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.tip-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.step-content {
  padding: 32rpx;
}

.step-header {
  text-align: center;
  margin-bottom: 48rpx;
}

.step-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.step-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.form-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
}

.form-item {
  margin-bottom: 32rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.form-tip {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

.coin-selector {
  display: flex;
  gap: 16rpx;
}

.coin-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  
  &.active {
    border-color: #667eea;
    background: #e8f2ff;
  }
}

.coin-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.coin-name {
  font-size: 24rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.coin-symbol {
  font-size: 20rpx;
  color: #999;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.checkbox-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.link-text {
  color: #667eea;
}

.mnemonic-warning {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  background: #fff2f0;
  border: 1rpx solid #ffccc7;
  border-radius: 12rpx;
  margin-bottom: 32rpx;
}

.warning-text {
  flex: 1;
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #cf1322;
  line-height: 1.4;
}

.mnemonic-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}

.mnemonic-word {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.word-index {
  width: 32rpx;
  font-size: 20rpx;
  color: #999;
  margin-right: 8rpx;
}

.word-text {
  font-size: 24rpx;
  color: #333;
}

.mnemonic-actions {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.copy-btn,
.screenshot-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  height: 80rpx;
  background: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 24rpx;
}

.backup-checkbox {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: #fff;
  border-radius: 12rpx;
}

.verify-container {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
}

.selected-words {
  min-height: 200rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border: 2rpx dashed #d0d0d0;
  border-radius: 12rpx;
  margin-bottom: 32rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.selected-word {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: #667eea;
  color: #fff;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.word-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.word-option {
  padding: 12rpx 24rpx;
  background: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #333;
  
  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

.verify-result {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-top: 24rpx;
}

.error-text {
  font-size: 24rpx;
  color: #FF6B6B;
}

.import-tab {
  display: flex;
  margin-bottom: 32rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #666;
  
  &.active {
    background: #fff;
    color: #667eea;
    font-weight: bold;
  }
}

.import-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 24rpx;
  line-height: 1.4;
  resize: none;
}

.hardware-list {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
}

.hardware-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.hardware-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 24rpx;
}

.hardware-info {
  flex: 1;
}

.hardware-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.hardware-desc {
  font-size: 24rpx;
  color: #666;
}

.hardware-status {
  font-size: 24rpx;
  color: #999;
}

.hardware-tips {
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
}

.tip-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.tip-item {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  background: #fff;
  border-top: 1rpx solid #e0e0e0;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
}

.prev-btn,
.next-btn {
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  border: none;
}

.prev-btn {
  flex: 1;
  background: #f8f9fa;
  color: #666;
}

.next-btn {
  flex: 2;
  background: #667eea;
  color: #fff;
  
  &.disabled {
    background: #d0d0d0;
    color: #999;
  }
}

.warning-popup,
.loading-popup {
  width: 500rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 16rpx;
  text-align: center;
}

.warning-title {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin: 24rpx 0 16rpx 0;
}

.warning-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 32rpx;
}

.warning-actions {
  display: flex;
  gap: 16rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 72rpx;
  border-radius: 8rpx;
  border: none;
  font-size: 26rpx;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #FF6B6B;
  color: #fff;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

.loading-text {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 16rpx;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
