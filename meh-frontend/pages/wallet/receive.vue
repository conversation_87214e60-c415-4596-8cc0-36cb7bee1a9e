<template>
  <view class="receive-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons type="left" size="24" color="#333" class="back-icon" @click="goBack"></uni-icons>
      <text class="nav-title">收款</text>
      <text class="nav-action" @click="openHistory">历史</text>
    </view>

    <!-- 钱包选择 -->
    <view class="wallet-selector">
      <view class="selector-header">
        <text class="selector-title">选择收款钱包</text>
      </view>
      
      <scroll-view class="wallet-scroll" scroll-x="true" show-scrollbar="false">
        <view 
          class="wallet-item" 
          v-for="wallet in walletList" 
          :key="wallet.id"
          :class="{ active: wallet.id === selectedWallet.id }"
          @click="selectWallet(wallet)"
        >
          <view class="wallet-icon">
            <image class="coin-icon" :src="getCoinIcon(wallet.coinType)" mode="aspectFit"></image>
          </view>
          <view class="wallet-info">
            <text class="wallet-name">{{ wallet.walletName }}</text>
            <text class="wallet-type">{{ wallet.coinType }}</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 二维码展示区 -->
    <view class="qr-section">
      <view class="qr-container">
        <view class="qr-code">
          <canvas 
            canvas-id="qrcode" 
            class="qr-canvas"
            :style="{ width: qrSize + 'px', height: qrSize + 'px' }"
          ></canvas>
        </view>
        
        <!-- 钱包信息叠加 -->
        <view class="qr-overlay">
          <view class="wallet-avatar">
            <image class="coin-icon" :src="getCoinIcon(selectedWallet.coinType)" mode="aspectFit"></image>
          </view>
        </view>
      </view>

      <!-- 地址信息 -->
      <view class="address-section">
        <view class="address-header">
          <text class="address-label">钱包地址</text>
          <view class="address-actions">
            <uni-icons type="copy" size="20" color="#667eea" @click="copyAddress"></uni-icons>
            <uni-icons type="share" size="20" color="#667eea" @click="shareAddress"></uni-icons>
          </view>
        </view>
        
        <view class="address-content" @click="copyAddress">
          <text class="address-text">{{ selectedWallet.walletAddress }}</text>
        </view>

        <!-- 网络信息 -->
        <view class="network-info">
          <text class="network-label">网络: {{ getNetworkName(selectedWallet.coinType) }}</text>
          <view class="network-status online">
            <text class="status-text">在线</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 收款金额设置 -->
    <view class="amount-section">
      <view class="section-header">
        <text class="section-title">设置收款金额</text>
        <text class="section-desc">(可选)</text>
      </view>

      <view class="amount-input">
        <input 
          class="amount-field"
          type="digit"
          placeholder="输入金额"
          v-model="receiveAmount"
          @input="onAmountChange"
        />
        <view class="currency-selector" @click="showCurrencyPicker">
          <text class="currency-text">{{ selectedCurrency }}</text>
          <uni-icons type="bottom" size="16" color="#999"></uni-icons>
        </view>
      </view>

      <view class="amount-converter" v-if="receiveAmount">
        <text class="convert-text">≈ {{ convertedAmount }} {{ selectedWallet.coinType }}</text>
      </view>
    </view>

    <!-- 收款备注 -->
    <view class="memo-section">
      <view class="section-header">
        <text class="section-title">收款备注</text>
        <text class="section-desc">(可选)</text>
      </view>

      <view class="memo-input">
        <textarea 
          class="memo-field"
          placeholder="添加收款备注，方便对方了解收款用途"
          v-model="receiveMemo"
          maxlength="100"
        ></textarea>
        <text class="memo-count">{{ receiveMemo.length }}/100</text>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <button class="action-btn secondary" @click="saveQRCode">
        <uni-icons type="download" size="20" color="#667eea"></uni-icons>
        <text class="btn-text">保存二维码</text>
      </button>
      
      <button class="action-btn primary" @click="shareReceive">
        <uni-icons type="share" size="20" color="#fff"></uni-icons>
        <text class="btn-text">分享收款</text>
      </button>
    </view>

    <!-- 收款记录 -->
    <view class="receive-history">
      <view class="section-header">
        <text class="section-title">最近收款</text>
        <text class="section-action" @click="openHistory">查看全部</text>
      </view>

      <view class="history-list">
        <view 
          class="history-item" 
          v-for="record in receiveRecords" 
          :key="record.id"
          @click="openReceiveDetail(record)"
        >
          <view class="record-icon">
            <uni-icons type="arrow-down" size="20" color="#4ECDC4"></uni-icons>
          </view>
          
          <view class="record-info">
            <view class="record-header">
              <text class="record-amount">+{{ record.amount }} {{ record.coinType }}</text>
              <text class="record-time">{{ formatTime(record.createTime) }}</text>
            </view>
            <view class="record-detail">
              <text class="record-from">来自: {{ formatAddress(record.fromAddress) }}</text>
              <text class="record-status" :class="record.status.toLowerCase()">{{ getStatusName(record.status) }}</text>
            </view>
          </view>
        </view>

        <view class="empty-history" v-if="receiveRecords.length === 0">
          <image class="empty-icon" src="/static/images/wallet/empty-receive.png" mode="aspectFit"></image>
          <text class="empty-text">暂无收款记录</text>
        </view>
      </view>
    </view>

    <!-- 货币选择弹窗 -->
    <uni-popup ref="currencyPopup" type="bottom">
      <view class="currency-picker">
        <view class="picker-header">
          <text class="picker-title">选择货币</text>
          <uni-icons type="close" size="24" @click="closeCurrencyPicker"></uni-icons>
        </view>
        
        <view class="currency-list">
          <view 
            class="currency-item" 
            v-for="currency in supportedCurrencies" 
            :key="currency.code"
            :class="{ active: currency.code === selectedCurrency }"
            @click="selectCurrency(currency.code)"
          >
            <text class="currency-name">{{ currency.name }}</text>
            <text class="currency-code">{{ currency.code }}</text>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 分享弹窗 -->
    <uni-popup ref="sharePopup" type="bottom">
      <view class="share-popup">
        <view class="share-header">
          <text class="share-title">分享收款信息</text>
          <uni-icons type="close" size="24" @click="closeSharePopup"></uni-icons>
        </view>
        
        <view class="share-methods">
          <view class="share-item" @click="shareToWeChat">
            <image class="share-icon" src="/static/images/share/wechat.png" mode="aspectFit"></image>
            <text class="share-text">微信</text>
          </view>
          <view class="share-item" @click="shareToMoments">
            <image class="share-icon" src="/static/images/share/moments.png" mode="aspectFit"></image>
            <text class="share-text">朋友圈</text>
          </view>
          <view class="share-item" @click="shareToBusinessCard">
            <image class="share-icon" src="/static/images/share/card.png" mode="aspectFit"></image>
            <text class="share-text">添加到名片</text>
          </view>
          <view class="share-item" @click="copyLink">
            <image class="share-icon" src="/static/images/share/link.png" mode="aspectFit"></image>
            <text class="share-text">复制链接</text>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 添加到名片弹窗 -->
    <uni-popup ref="cardAddPopup" type="center">
      <view class="card-add-popup">
        <text class="popup-title">添加到名片</text>
        <text class="popup-desc">将钱包地址添加到您的电子名片中</text>
        
        <view class="card-list">
          <view 
            class="card-item" 
            v-for="card in userCards" 
            :key="card.id"
            @click="addToCard(card.id)"
          >
            <image class="card-avatar" :src="card.avatar" mode="aspectFill"></image>
            <view class="card-info">
              <text class="card-name">{{ card.name }}</text>
              <text class="card-position">{{ card.position }}</text>
            </view>
            <uni-icons type="checkmarkempty" size="20" color="#667eea"></uni-icons>
          </view>
        </view>

        <view class="popup-actions">
          <button class="cancel-btn" @click="closeCardAddPopup">取消</button>
          <button class="confirm-btn" @click="createNewCardWithWallet">创建新名片</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import QRCode from '@/utils/qrcode.js' // 需要引入二维码生成库

export default {
  data() {
    return {
      // 钱包列表
      walletList: [],
      
      // 选中的钱包
      selectedWallet: {
        id: null,
        walletName: '',
        walletAddress: '',
        coinType: 'ETH'
      },
      
      // 收款金额
      receiveAmount: '',
      convertedAmount: '',
      
      // 选中的货币
      selectedCurrency: 'USD',
      
      // 支持的货币
      supportedCurrencies: [
        { code: 'USD', name: '美元' },
        { code: 'CNY', name: '人民币' },
        { code: 'EUR', name: '欧元' },
        { code: 'JPY', name: '日元' }
      ],
      
      // 收款备注
      receiveMemo: '',
      
      // 二维码大小
      qrSize: 200,
      
      // 收款记录
      receiveRecords: [],
      
      // 用户名片
      userCards: [],
      
      // 汇率数据
      exchangeRates: {}
    }
  },
  
  onLoad(options) {
    if (options.walletId) {
      this.selectedWalletId = options.walletId
    }
    this.loadWalletList()
    this.loadReceiveRecords()
    this.loadUserCards()
    this.loadExchangeRates()
  },
  
  watch: {
    selectedWallet: {
      handler(newWallet) {
        if (newWallet.walletAddress) {
          this.generateQRCode()
        }
      },
      deep: true
    },
    
    receiveAmount() {
      this.updateConvertedAmount()
    },
    
    receiveMemo() {
      this.generateQRCode()
    }
  },
  
  methods: {
    // 加载钱包列表
    loadWalletList() {
      this.$api.get('/wallets').then(res => {
        this.walletList = res.data || []
        if (this.walletList.length > 0) {
          const targetWallet = this.selectedWalletId 
            ? this.walletList.find(w => w.id == this.selectedWalletId)
            : this.walletList[0]
          this.selectedWallet = targetWallet || this.walletList[0]
        }
      }).catch(err => {
        console.error('加载钱包列表失败:', err)
        uni.showToast({
          title: '加载钱包失败',
          icon: 'error'
        })
      })
    },
    
    // 加载收款记录
    loadReceiveRecords() {
      this.$api.get('/wallet-transactions/history', {
        params: {
          walletId: this.selectedWallet.id,
          txType: 'RECEIVE',
          pageSize: 5
        }
      }).then(res => {
        this.receiveRecords = res.data.records || []
      })
    },
    
    // 加载用户名片
    loadUserCards() {
      this.$api.get('/cards').then(res => {
        this.userCards = res.data || []
      })
    },
    
    // 加载汇率数据
    loadExchangeRates() {
      this.$api.get('/market/exchange-rates').then(res => {
        this.exchangeRates = res.data || {}
      })
    },
    
    // 选择钱包
    selectWallet(wallet) {
      this.selectedWallet = wallet
      this.loadReceiveRecords()
    },
    
    // 生成二维码
    generateQRCode() {
      if (!this.selectedWallet.walletAddress) return
      
      // 构建收款链接
      const receiveData = {
        address: this.selectedWallet.walletAddress,
        coinType: this.selectedWallet.coinType,
        amount: this.receiveAmount || undefined,
        memo: this.receiveMemo || undefined,
        network: this.getNetworkName(this.selectedWallet.coinType)
      }
      
      const qrContent = `meh://receive?${this.encodeReceiveData(receiveData)}`
      
      // 生成二维码
      this.$nextTick(() => {
        const ctx = uni.createCanvasContext('qrcode', this)
        
        // 使用二维码库生成
        QRCode.toCanvas(ctx, qrContent, {
          width: this.qrSize,
          height: this.qrSize,
          margin: 1,
          color: {
            dark: '#333333',
            light: '#FFFFFF'
          }
        }, (error) => {
          if (error) {
            console.error('二维码生成失败:', error)
          } else {
            ctx.draw()
          }
        })
      })
    },
    
    // 编码收款数据
    encodeReceiveData(data) {
      return Object.keys(data)
        .filter(key => data[key] !== undefined)
        .map(key => `${key}=${encodeURIComponent(data[key])}`)
        .join('&')
    },
    
    // 金额变化处理
    onAmountChange() {
      this.updateConvertedAmount()
      this.generateQRCode()
    },
    
    // 更新换算金额
    updateConvertedAmount() {
      if (!this.receiveAmount || !this.exchangeRates[this.selectedWallet.coinType]) {
        this.convertedAmount = ''
        return
      }
      
      const rate = this.exchangeRates[this.selectedWallet.coinType][this.selectedCurrency]
      if (rate) {
        const amount = parseFloat(this.receiveAmount) / rate
        this.convertedAmount = amount.toFixed(6)
      }
    },
    
    // 显示货币选择器
    showCurrencyPicker() {
      this.$refs.currencyPopup.open()
    },
    
    // 关闭货币选择器
    closeCurrencyPicker() {
      this.$refs.currencyPopup.close()
    },
    
    // 选择货币
    selectCurrency(currency) {
      this.selectedCurrency = currency
      this.updateConvertedAmount()
      this.closeCurrencyPicker()
    },
    
    // 复制地址
    copyAddress() {
      uni.setClipboardData({
        data: this.selectedWallet.walletAddress,
        success: () => {
          uni.showToast({
            title: '地址已复制',
            icon: 'success'
          })
        }
      })
    },
    
    // 分享地址
    shareAddress() {
      this.openSharePopup()
    },
    
    // 保存二维码
    saveQRCode() {
      uni.canvasToTempFilePath({
        canvasId: 'qrcode',
        success: (res) => {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              uni.showToast({
                title: '二维码已保存',
                icon: 'success'
              })
            },
            fail: () => {
              uni.showToast({
                title: '保存失败',
                icon: 'error'
              })
            }
          })
        }
      }, this)
    },
    
    // 分享收款
    shareReceive() {
      this.openSharePopup()
    },
    
    // 打开分享弹窗
    openSharePopup() {
      this.$refs.sharePopup.open()
    },
    
    // 关闭分享弹窗
    closeSharePopup() {
      this.$refs.sharePopup.close()
    },
    
    // 分享到微信
    shareToWeChat() {
      // 实现微信分享
      console.log('分享到微信')
      this.closeSharePopup()
    },
    
    // 分享到朋友圈
    shareToMoments() {
      // 实现朋友圈分享
      console.log('分享到朋友圈')
      this.closeSharePopup()
    },
    
    // 分享到名片
    shareToBusinessCard() {
      this.closeSharePopup()
      this.$refs.cardAddPopup.open()
    },
    
    // 复制链接
    copyLink() {
      const receiveData = {
        address: this.selectedWallet.walletAddress,
        coinType: this.selectedWallet.coinType,
        amount: this.receiveAmount || undefined,
        memo: this.receiveMemo || undefined
      }
      
      const link = `https://meh.business/receive?${this.encodeReceiveData(receiveData)}`
      
      uni.setClipboardData({
        data: link,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success'
          })
          this.closeSharePopup()
        }
      })
    },
    
    // 添加到名片
    addToCard(cardId) {
      this.$api.post(`/cards/${cardId}/wallet`, {
        walletId: this.selectedWallet.id,
        walletAddress: this.selectedWallet.walletAddress,
        coinType: this.selectedWallet.coinType
      }).then(() => {
        uni.showToast({
          title: '添加成功',
          icon: 'success'
        })
        this.closeCardAddPopup()
      }).catch(err => {
        uni.showToast({
          title: '添加失败',
          icon: 'error'
        })
      })
    },
    
    // 创建新名片并添加钱包
    createNewCardWithWallet() {
      this.closeCardAddPopup()
      uni.navigateTo({
        url: `/pages/card/create?walletAddress=${this.selectedWallet.walletAddress}&coinType=${this.selectedWallet.coinType}`
      })
    },
    
    // 关闭名片添加弹窗
    closeCardAddPopup() {
      this.$refs.cardAddPopup.close()
    },
    
    // 打开收款详情
    openReceiveDetail(record) {
      uni.navigateTo({
        url: `/pages/wallet/transaction-detail?txId=${record.id}`
      })
    },
    
    // 打开历史记录
    openHistory() {
      uni.navigateTo({
        url: `/pages/wallet/transaction-history?walletId=${this.selectedWallet.id}&type=receive`
      })
    },
    
    // 获取币种图标
    getCoinIcon(coinType) {
      return `/static/images/coins/${coinType.toLowerCase()}.png`
    },
    
    // 获取网络名称
    getNetworkName(coinType) {
      const networkMap = {
        'ETH': 'Ethereum',
        'BTC': 'Bitcoin',
        'USDT': 'Ethereum'
      }
      return networkMap[coinType] || coinType
    },
    
    // 格式化地址
    formatAddress(address) {
      if (!address) return ''
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    },
    
    // 格式化时间
    formatTime(time) {
      return new Date(time).toLocaleString()
    },
    
    // 获取状态名称
    getStatusName(status) {
      const statusMap = {
        'PENDING': '待确认',
        'SUCCESS': '成功',
        'FAILED': '失败'
      }
      return statusMap[status] || status
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss">
.receive-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height);
  background: #fff;
  border-bottom: 1rpx solid #e0e0e0;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.nav-action {
  font-size: 28rpx;
  color: #667eea;
}

.wallet-selector {
  margin: 24rpx 32rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
}

.selector-header {
  margin-bottom: 24rpx;
}

.selector-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.wallet-scroll {
  white-space: nowrap;
}

.wallet-item {
  display: inline-flex;
  align-items: center;
  padding: 20rpx;
  margin-right: 16rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  min-width: 180rpx;
  
  &.active {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
  }
}

.wallet-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 16rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coin-icon {
  width: 40rpx;
  height: 40rpx;
}

.wallet-info {
  flex: 1;
}

.wallet-name {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.wallet-type {
  font-size: 22rpx;
  color: #999;
}

.qr-section {
  margin: 0 32rpx 24rpx 32rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 16rpx;
  text-align: center;
}

.qr-container {
  position: relative;
  display: inline-block;
  margin-bottom: 32rpx;
}

.qr-canvas {
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.qr-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.wallet-avatar {
  width: 60rpx;
  height: 60rpx;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.address-section {
  text-align: left;
}

.address-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.address-label {
  font-size: 26rpx;
  color: #666;
}

.address-actions {
  display: flex;
  gap: 16rpx;
}

.address-content {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.address-text {
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
}

.network-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.network-label {
  font-size: 24rpx;
  color: #666;
}

.network-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  
  &.online {
    background: rgba(76, 205, 196, 0.1);
    color: #4ECDC4;
  }
}

.amount-section,
.memo-section {
  margin: 0 32rpx 24rpx 32rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.section-desc {
  font-size: 24rpx;
  color: #999;
}

.amount-input {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.amount-field {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  border: none;
  background: transparent;
}

.currency-selector {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #fff;
  border-radius: 8rpx;
}

.currency-text {
  font-size: 26rpx;
  color: #333;
}

.amount-converter {
  text-align: center;
}

.convert-text {
  font-size: 24rpx;
  color: #666;
}

.memo-input {
  position: relative;
}

.memo-field {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #333;
  border: none;
  resize: none;
}

.memo-count {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  font-size: 22rpx;
  color: #999;
}

.quick-actions {
  display: flex;
  gap: 16rpx;
  margin: 0 32rpx 32rpx 32rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 28rpx;
  
  &.primary {
    background: #667eea;
    color: #fff;
  }
  
  &.secondary {
    background: #fff;
    color: #667eea;
    border: 2rpx solid #667eea;
  }
}

.receive-history {
  margin: 0 32rpx 32rpx 32rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
}

.section-action {
  font-size: 24rpx;
  color: #667eea;
}

.history-list {
  margin-top: 24rpx;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.record-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 24rpx;
  border-radius: 50%;
  background: rgba(76, 205, 196, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.record-info {
  flex: 1;
}

.record-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.record-amount {
  font-size: 28rpx;
  font-weight: bold;
  color: #4ECDC4;
}

.record-time {
  font-size: 22rpx;
  color: #999;
}

.record-detail {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.record-from {
  font-size: 24rpx;
  color: #666;
}

.record-status {
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  
  &.success {
    background: rgba(76, 205, 196, 0.1);
    color: #4ECDC4;
  }
  
  &.pending {
    background: rgba(255, 193, 7, 0.1);
    color: #FFC107;
  }
}

.currency-picker,
.share-popup {
  padding: 32rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.picker-header,
.share-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.picker-title,
.share-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.currency-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.currency-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &.active {
    color: #667eea;
  }
}

.currency-name {
  font-size: 28rpx;
}

.currency-code {
  font-size: 24rpx;
  color: #999;
}

.share-methods {
  display: flex;
  justify-content: space-around;
}

.share-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
}

.share-icon {
  width: 80rpx;
  height: 80rpx;
}

.share-text {
  font-size: 24rpx;
  color: #333;
}

.card-add-popup {
  width: 600rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 24rpx;
}

.popup-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16rpx;
}

.popup-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-bottom: 32rpx;
}

.card-list {
  margin-bottom: 32rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.card-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  margin-bottom: 16rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
}

.card-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.card-info {
  flex: 1;
}

.card-name {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.card-position {
  font-size: 22rpx;
  color: #666;
}

.popup-actions {
  display: flex;
  gap: 16rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #667eea;
  color: #fff;
}

.empty-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
