<template>
  <view class="transfer-container">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <uni-icons type="left" size="24" color="#333" class="back-icon" @click="goBack"></uni-icons>
      <text class="nav-title">转账</text>
      <uni-icons type="scan" size="24" color="#333" class="scan-icon" @click="openScan"></uni-icons>
    </view>

    <!-- 钱包选择 -->
    <view class="wallet-section">
      <view class="section-header">
        <text class="section-title">从钱包</text>
      </view>
      
      <view class="wallet-card" @click="showWalletSelector">
        <view class="wallet-info">
          <view class="wallet-icon">
            <image class="coin-icon" :src="getCoinIcon(selectedWallet.coinType)" mode="aspectFit"></image>
          </view>
          <view class="wallet-details">
            <text class="wallet-name">{{ selectedWallet.walletName }}</text>
            <text class="wallet-balance">{{ selectedWallet.balance }} {{ selectedWallet.coinType }}</text>
          </view>
        </view>
        <uni-icons type="right" size="20" color="#999"></uni-icons>
      </view>
    </view>

    <!-- 收款方输入 -->
    <view class="recipient-section">
      <view class="section-header">
        <text class="section-title">收款方</text>
      </view>
      
      <view class="recipient-input-container">
        <input 
          class="recipient-input" 
          v-model="transferForm.recipientAddress"
          placeholder="输入钱包地址或扫码"
          type="text"
        />
        <view class="input-actions">
          <uni-icons type="scan" size="20" color="#667eea" @click="openScan"></uni-icons>
          <uni-icons type="contact" size="20" color="#667eea" @click="openAddressBook"></uni-icons>
        </view>
      </view>

      <!-- 最近联系人 -->
      <view class="recent-contacts" v-if="recentContacts.length > 0">
        <text class="contacts-title">最近联系人</text>
        <scroll-view class="contacts-scroll" scroll-x="true" show-scrollbar="false">
          <view 
            class="contact-item" 
            v-for="contact in recentContacts" 
            :key="contact.id"
            @click="selectContact(contact)"
          >
            <view class="contact-avatar">
              <image v-if="contact.avatar" :src="contact.avatar" mode="aspectFill"></image>
              <uni-icons v-else type="person" size="20" color="#999"></uni-icons>
            </view>
            <text class="contact-name">{{ contact.name }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 地址验证状态 -->
      <view class="address-validation" v-if="transferForm.recipientAddress">
        <view class="validation-item" :class="addressValidation.isValid ? 'valid' : 'invalid'">
          <uni-icons :type="addressValidation.isValid ? 'checkmarkempty' : 'closeempty'" size="16"></uni-icons>
          <text class="validation-text">{{ addressValidation.message }}</text>
        </view>
      </view>
    </view>

    <!-- 转账金额 -->
    <view class="amount-section">
      <view class="section-header">
        <text class="section-title">转账金额</text>
        <text class="available-balance">可用: {{ selectedWallet.balance }} {{ selectedWallet.coinType }}</text>
      </view>

      <view class="amount-input-container">
        <input 
          class="amount-input" 
          v-model="transferForm.amount"
          placeholder="0.00"
          type="digit"
          @input="onAmountChange"
        />
        <view class="currency-display">
          <text class="currency-text">{{ selectedWallet.coinType }}</text>
        </view>
      </view>

      <view class="amount-converter" v-if="transferForm.amount">
        <text class="convert-text">≈ ¥{{ convertedAmount }} CNY</text>
      </view>

      <!-- 快捷金额 -->
      <view class="quick-amounts">
        <view 
          class="quick-amount-btn" 
          v-for="percentage in [25, 50, 75, 100]" 
          :key="percentage"
          @click="setAmountPercentage(percentage)"
        >
          <text class="amount-text">{{ percentage }}%</text>
        </view>
      </view>
    </view>

    <!-- 矿工费设置 -->
    <view class="gas-section">
      <view class="section-header">
        <text class="section-title">网络费用</text>
        <text class="gas-info" @click="showGasInfo">什么是网络费用?</text>
      </view>

      <view class="gas-options">
        <view 
          class="gas-option" 
          v-for="option in gasOptions" 
          :key="option.type"
          :class="{ active: transferForm.gasOption === option.type }"
          @click="selectGasOption(option.type)"
        >
          <view class="gas-info">
            <text class="gas-type">{{ option.label }}</text>
            <text class="gas-time">{{ option.time }}</text>
          </view>
          <view class="gas-fee">
            <text class="fee-amount">{{ option.fee }} {{ selectedWallet.coinType }}</text>
            <text class="fee-fiat">≈ ¥{{ (option.fee * exchangeRate).toFixed(2) }}</text>
          </view>
        </view>
      </view>

      <!-- 自定义矿工费 -->
      <view class="custom-gas" v-if="showCustomGas">
        <view class="custom-gas-input">
          <input 
            class="gas-input" 
            v-model="transferForm.customGasFee"
            placeholder="自定义费用"
            type="digit"
          />
          <text class="gas-unit">{{ selectedWallet.coinType }}</text>
        </view>
      </view>

      <view class="gas-toggle">
        <text class="toggle-text" @click="showCustomGas = !showCustomGas">
          {{ showCustomGas ? '使用预设费用' : '自定义费用' }}
        </text>
      </view>
    </view>

    <!-- 高级选项 -->
    <view class="advanced-section">
      <view class="section-toggle" @click="showAdvanced = !showAdvanced">
        <text class="toggle-title">高级选项</text>
        <uni-icons :type="showAdvanced ? 'up' : 'down'" size="16" color="#999"></uni-icons>
      </view>

      <view class="advanced-content" v-if="showAdvanced">
        <!-- 交易备注 -->
        <view class="memo-input">
          <text class="input-label">备注 (可选)</text>
          <textarea 
            class="memo-field"
            v-model="transferForm.memo"
            placeholder="添加交易备注..."
            maxlength="200"
          ></textarea>
          <text class="memo-count">{{ transferForm.memo.length }}/200</text>
        </view>

        <!-- 定时转账 -->
        <view class="schedule-option">
          <view class="option-header" @click="transferForm.isScheduled = !transferForm.isScheduled">
            <text class="option-title">定时转账</text>
            <switch :checked="transferForm.isScheduled" @change="onScheduleChange"></switch>
          </view>
          
          <view class="schedule-content" v-if="transferForm.isScheduled">
            <picker 
              mode="date" 
              :value="transferForm.scheduleDate"
              @change="onDateChange"
            >
              <view class="picker-item">
                <text class="picker-label">日期</text>
                <text class="picker-value">{{ transferForm.scheduleDate || '选择日期' }}</text>
              </view>
            </picker>
            
            <picker 
              mode="time" 
              :value="transferForm.scheduleTime"
              @change="onTimeChange"
            >
              <view class="picker-item">
                <text class="picker-label">时间</text>
                <text class="picker-value">{{ transferForm.scheduleTime || '选择时间' }}</text>
              </view>
            </picker>
          </view>
        </view>

        <!-- 批量转账 -->
        <view class="batch-option">
          <view class="option-header" @click="transferForm.isBatch = !transferForm.isBatch">
            <text class="option-title">批量转账</text>
            <switch :checked="transferForm.isBatch" @change="onBatchChange"></switch>
          </view>
          
          <view class="batch-content" v-if="transferForm.isBatch">
            <text class="batch-desc">添加多个收款地址进行批量转账</text>
            <button class="add-recipient-btn" @click="addBatchRecipient">
              <uni-icons type="plus" size="16" color="#667eea"></uni-icons>
              <text class="btn-text">添加收款方</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 交易摘要 -->
    <view class="summary-section">
      <view class="section-header">
        <text class="section-title">交易摘要</text>
      </view>

      <view class="summary-content">
        <view class="summary-item">
          <text class="item-label">转账金额</text>
          <text class="item-value">{{ transferForm.amount || '0.00' }} {{ selectedWallet.coinType }}</text>
        </view>
        
        <view class="summary-item">
          <text class="item-label">网络费用</text>
          <text class="item-value">{{ selectedGasFee }} {{ selectedWallet.coinType }}</text>
        </view>
        
        <view class="summary-divider"></view>
        
        <view class="summary-item total">
          <text class="item-label">总计</text>
          <text class="item-value">{{ totalAmount }} {{ selectedWallet.coinType }}</text>
        </view>
        
        <view class="summary-fiat">
          <text class="fiat-text">≈ ¥{{ totalAmountFiat }} CNY</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button 
        class="transfer-btn" 
        :class="{ disabled: !isFormValid }"
        :disabled="!isFormValid"
        @click="reviewTransfer"
      >
        <text class="btn-text">{{ transferForm.isScheduled ? '安排转账' : '确认转账' }}</text>
      </button>
    </view>

    <!-- 钱包选择弹窗 -->
    <uni-popup ref="walletPopup" type="bottom">
      <view class="wallet-popup">
        <view class="popup-header">
          <text class="popup-title">选择钱包</text>
          <uni-icons type="close" size="24" @click="closeWalletPopup"></uni-icons>
        </view>
        
        <view class="wallet-list">
          <view 
            class="wallet-item" 
            v-for="wallet in walletList" 
            :key="wallet.id"
            @click="selectWallet(wallet)"
          >
            <view class="wallet-info">
              <image class="coin-icon" :src="getCoinIcon(wallet.coinType)" mode="aspectFit"></image>
              <view class="wallet-details">
                <text class="wallet-name">{{ wallet.walletName }}</text>
                <text class="wallet-balance">{{ wallet.balance }} {{ wallet.coinType }}</text>
              </view>
            </view>
            <uni-icons v-if="wallet.id === selectedWallet.id" type="checkmarkempty" size="20" color="#667eea"></uni-icons>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 地址簿弹窗 -->
    <uni-popup ref="addressBookPopup" type="bottom">
      <view class="address-book-popup">
        <view class="popup-header">
          <text class="popup-title">地址簿</text>
          <uni-icons type="close" size="24" @click="closeAddressBookPopup"></uni-icons>
        </view>
        
        <view class="search-container">
          <view class="search-input">
            <uni-icons type="search" size="16" color="#999"></uni-icons>
            <input v-model="searchKeyword" placeholder="搜索联系人或地址" />
          </view>
        </view>
        
        <view class="address-list">
          <view 
            class="address-item" 
            v-for="contact in filteredContacts" 
            :key="contact.id"
            @click="selectContact(contact)"
          >
            <view class="contact-avatar">
              <image v-if="contact.avatar" :src="contact.avatar" mode="aspectFill"></image>
              <uni-icons v-else type="person" size="24" color="#999"></uni-icons>
            </view>
            <view class="contact-info">
              <text class="contact-name">{{ contact.name }}</text>
              <text class="contact-address">{{ formatAddress(contact.walletAddress) }}</text>
            </view>
            <view class="contact-type">
              <text class="type-tag">{{ contact.source || '联系人' }}</text>
            </view>
          </view>

          <view class="empty-contacts" v-if="filteredContacts.length === 0">
            <image class="empty-icon" src="/static/images/wallet/empty-contacts.png" mode="aspectFit"></image>
            <text class="empty-text">暂无联系人</text>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 转账确认弹窗 -->
    <uni-popup ref="confirmPopup" type="center">
      <view class="confirm-popup">
        <text class="confirm-title">确认转账</text>
        
        <view class="confirm-content">
          <view class="confirm-item">
            <text class="confirm-label">从</text>
            <view class="confirm-wallet">
              <image class="coin-icon" :src="getCoinIcon(selectedWallet.coinType)" mode="aspectFit"></image>
              <text class="wallet-name">{{ selectedWallet.walletName }}</text>
            </view>
          </view>
          
          <view class="confirm-item">
            <text class="confirm-label">到</text>
            <text class="confirm-address">{{ formatAddress(transferForm.recipientAddress) }}</text>
          </view>
          
          <view class="confirm-item">
            <text class="confirm-label">金额</text>
            <text class="confirm-amount">{{ transferForm.amount }} {{ selectedWallet.coinType }}</text>
          </view>
          
          <view class="confirm-item">
            <text class="confirm-label">网络费用</text>
            <text class="confirm-fee">{{ selectedGasFee }} {{ selectedWallet.coinType }}</text>
          </view>
          
          <view class="confirm-item total">
            <text class="confirm-label">总计</text>
            <text class="confirm-total">{{ totalAmount }} {{ selectedWallet.coinType }}</text>
          </view>
          
          <view v-if="transferForm.memo" class="confirm-item">
            <text class="confirm-label">备注</text>
            <text class="confirm-memo">{{ transferForm.memo }}</text>
          </view>
        </view>

        <!-- 安全验证 -->
        <view class="security-section">
          <text class="security-title">安全验证</text>
          <view class="security-options">
            <view class="security-option" @click="usePasswordAuth">
              <uni-icons type="locked" size="20" color="#667eea"></uni-icons>
              <text class="security-text">交易密码</text>
            </view>
            <view class="security-option" @click="useFingerprintAuth">
              <uni-icons type="scan" size="20" color="#667eea"></uni-icons>
              <text class="security-text">指纹验证</text>
            </view>
          </view>
        </view>

        <view class="confirm-actions">
          <button class="cancel-btn" @click="closeConfirmPopup">取消</button>
          <button class="confirm-btn" @click="confirmTransfer" :disabled="isProcessing">
            {{ isProcessing ? '处理中...' : '确认转账' }}
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 密码验证弹窗 -->
    <uni-popup ref="passwordPopup" type="center">
      <view class="password-popup">
        <text class="password-title">输入交易密码</text>
        
        <view class="password-input">
          <input 
            v-model="transactionPassword"
            type="password"
            placeholder="请输入6位数字密码"
            maxlength="6"
            @input="onPasswordInput"
          />
        </view>
        
        <view class="password-actions">
          <button class="cancel-btn" @click="closePasswordPopup">取消</button>
          <button class="confirm-btn" @click="verifyPassword" :disabled="transactionPassword.length !== 6">
            确认
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 扫码弹窗 -->
    <uni-popup ref="scanPopup" type="center">
      <view class="scan-popup">
        <text class="scan-title">扫描二维码</text>
        <text class="scan-desc">扫描收款方的钱包地址二维码</text>
        
        <view class="scan-frame">
          <view class="scan-area">
            <view class="scan-corners"></view>
            <text class="scan-tip">将二维码放入框内即可自动扫描</text>
          </view>
        </view>
        
        <view class="scan-actions">
          <button class="scan-cancel-btn" @click="closeScanPopup">取消</button>
          <button class="scan-album-btn" @click="chooseFromAlbum">从相册选择</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 选中的钱包
      selectedWallet: {
        id: null,
        walletName: '',
        balance: '0.00',
        coinType: 'ETH',
        walletAddress: ''
      },
      
      // 钱包列表
      walletList: [],
      
      // 转账表单
      transferForm: {
        recipientAddress: '',
        amount: '',
        gasOption: 'standard',
        customGasFee: '',
        memo: '',
        isScheduled: false,
        scheduleDate: '',
        scheduleTime: '',
        isBatch: false,
        batchRecipients: []
      },
      
      // 矿工费选项
      gasOptions: [
        {
          type: 'slow',
          label: '慢速',
          time: '~5分钟',
          fee: '0.0021'
        },
        {
          type: 'standard',
          label: '标准',
          time: '~2分钟',
          fee: '0.0042'
        },
        {
          type: 'fast',
          label: '快速',
          time: '~30秒',
          fee: '0.0084'
        }
      ],
      
      // 最近联系人
      recentContacts: [],
      
      // 地址簿联系人
      addressBookContacts: [],
      
      // 搜索关键词
      searchKeyword: '',
      
      // 地址验证
      addressValidation: {
        isValid: false,
        message: ''
      },
      
      // 汇率
      exchangeRate: 15000, // ETH to CNY
      
      // 界面状态
      showAdvanced: false,
      showCustomGas: false,
      isProcessing: false,
      
      // 安全验证
      transactionPassword: '',
      
      // 其他状态
      walletId: null
    }
  },
  
  computed: {
    // 过滤后的联系人
    filteredContacts() {
      if (!this.searchKeyword) {
        return this.addressBookContacts
      }
      
      return this.addressBookContacts.filter(contact => 
        contact.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
        contact.walletAddress.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    },
    
    // 选中的矿工费
    selectedGasFee() {
      if (this.showCustomGas && this.transferForm.customGasFee) {
        return this.transferForm.customGasFee
      }
      
      const option = this.gasOptions.find(opt => opt.type === this.transferForm.gasOption)
      return option ? option.fee : '0.0042'
    },
    
    // 转换后的金额
    convertedAmount() {
      if (!this.transferForm.amount) return '0.00'
      return (parseFloat(this.transferForm.amount) * this.exchangeRate).toFixed(2)
    },
    
    // 总金额
    totalAmount() {
      const amount = parseFloat(this.transferForm.amount || 0)
      const gasFee = parseFloat(this.selectedGasFee)
      return (amount + gasFee).toFixed(6)
    },
    
    // 总金额法币
    totalAmountFiat() {
      return (parseFloat(this.totalAmount) * this.exchangeRate).toFixed(2)
    },
    
    // 表单验证
    isFormValid() {
      return this.transferForm.recipientAddress && 
             this.transferForm.amount && 
             parseFloat(this.transferForm.amount) > 0 &&
             this.addressValidation.isValid &&
             parseFloat(this.totalAmount) <= parseFloat(this.selectedWallet.balance)
    }
  },
  
  onLoad(options) {
    if (options.walletId) {
      this.walletId = options.walletId
    }
    
    this.loadWalletList()
    this.loadRecentContacts()
    this.loadAddressBook()
    this.loadExchangeRate()
  },
  
  watch: {
    'transferForm.recipientAddress'() {
      this.validateAddress()
    }
  },
  
  methods: {
    // 加载钱包列表
    loadWalletList() {
      this.$api.get('/wallets').then(res => {
        this.walletList = res.data || []
        if (this.walletList.length > 0) {
          const targetWallet = this.walletId 
            ? this.walletList.find(w => w.id == this.walletId)
            : this.walletList[0]
          this.selectedWallet = targetWallet || this.walletList[0]
        }
      }).catch(err => {
        console.error('加载钱包列表失败:', err)
      })
    },
    
    // 加载最近联系人
    loadRecentContacts() {
      this.$api.get('/wallet-address-book/recent').then(res => {
        this.recentContacts = res.data || []
      })
    },
    
    // 加载地址簿
    loadAddressBook() {
      this.$api.get('/wallet-address-book').then(res => {
        this.addressBookContacts = res.data || []
      })
    },
    
    // 加载汇率
    loadExchangeRate() {
      this.$api.get('/market/exchange-rates').then(res => {
        if (res.data && res.data[this.selectedWallet.coinType]) {
          this.exchangeRate = res.data[this.selectedWallet.coinType]['CNY']
        }
      })
    },
    
    // 显示钱包选择器
    showWalletSelector() {
      this.$refs.walletPopup.open()
    },
    
    // 关闭钱包选择器
    closeWalletPopup() {
      this.$refs.walletPopup.close()
    },
    
    // 选择钱包
    selectWallet(wallet) {
      this.selectedWallet = wallet
      this.loadExchangeRate()
      this.closeWalletPopup()
    },
    
    // 打开地址簿
    openAddressBook() {
      this.$refs.addressBookPopup.open()
    },
    
    // 关闭地址簿
    closeAddressBookPopup() {
      this.$refs.addressBookPopup.close()
    },
    
    // 选择联系人
    selectContact(contact) {
      this.transferForm.recipientAddress = contact.walletAddress
      this.closeAddressBookPopup()
    },
    
    // 金额变化处理
    onAmountChange() {
      // 验证金额格式
      if (this.transferForm.amount && !/^\d*\.?\d*$/.test(this.transferForm.amount)) {
        this.transferForm.amount = this.transferForm.amount.replace(/[^\d.]/g, '')
      }
    },
    
    // 设置百分比金额
    setAmountPercentage(percentage) {
      const balance = parseFloat(this.selectedWallet.balance)
      const gasFee = parseFloat(this.selectedGasFee)
      const availableAmount = Math.max(0, balance - gasFee)
      const amount = (availableAmount * percentage / 100).toFixed(6)
      this.transferForm.amount = amount
    },
    
    // 选择矿工费选项
    selectGasOption(type) {
      this.transferForm.gasOption = type
      this.showCustomGas = false
    },
    
    // 地址验证
    validateAddress() {
      const address = this.transferForm.recipientAddress
      
      if (!address) {
        this.addressValidation = { isValid: false, message: '' }
        return
      }
      
      // 简单的以太坊地址验证
      if (address.startsWith('0x') && address.length === 42) {
        this.addressValidation = { isValid: true, message: '地址格式正确' }
      } else {
        this.addressValidation = { isValid: false, message: '地址格式错误' }
      }
    },
    
    // 定时转账开关
    onScheduleChange(e) {
      this.transferForm.isScheduled = e.detail.value
    },
    
    // 批量转账开关
    onBatchChange(e) {
      this.transferForm.isBatch = e.detail.value
    },
    
    // 日期选择
    onDateChange(e) {
      this.transferForm.scheduleDate = e.detail.value
    },
    
    // 时间选择
    onTimeChange(e) {
      this.transferForm.scheduleTime = e.detail.value
    },
    
    // 添加批量收款方
    addBatchRecipient() {
      uni.navigateTo({
        url: '/pages/wallet/batch-transfer'
      })
    },
    
    // 确认转账
    reviewTransfer() {
      if (!this.isFormValid) {
        uni.showToast({
          title: '请检查输入信息',
          icon: 'error'
        })
        return
      }
      
      this.$refs.confirmPopup.open()
    },
    
    // 关闭确认弹窗
    closeConfirmPopup() {
      this.$refs.confirmPopup.close()
    },
    
    // 使用密码验证
    usePasswordAuth() {
      this.closeConfirmPopup()
      this.$refs.passwordPopup.open()
    },
    
    // 使用指纹验证
    useFingerprintAuth() {
      // 实现指纹验证
      uni.startSoterAuthentication({
        requestAuthModes: ['fingerPrint'],
        challenge: '123456',
        authContent: '请验证指纹以确认转账',
        success: (res) => {
          this.executeTransfer()
        },
        fail: (err) => {
          uni.showToast({
            title: '指纹验证失败',
            icon: 'error'
          })
        }
      })
    },
    
    // 关闭密码弹窗
    closePasswordPopup() {
      this.$refs.passwordPopup.close()
      this.transactionPassword = ''
    },
    
    // 密码输入处理
    onPasswordInput() {
      // 只允许数字
      this.transactionPassword = this.transactionPassword.replace(/[^\d]/g, '')
    },
    
    // 验证密码
    verifyPassword() {
      if (this.transactionPassword.length !== 6) {
        uni.showToast({
          title: '请输入6位数字密码',
          icon: 'error'
        })
        return
      }
      
      // 验证密码
      this.$api.post('/wallet-security/verify-password', {
        password: this.transactionPassword
      }).then(() => {
        this.closePasswordPopup()
        this.executeTransfer()
      }).catch(err => {
        uni.showToast({
          title: '密码错误',
          icon: 'error'
        })
      })
    },
    
    // 执行转账
    confirmTransfer() {
      this.executeTransfer()
    },
    
    // 执行转账
    executeTransfer() {
      this.isProcessing = true
      
      const transferData = {
        walletId: this.selectedWallet.id,
        recipientAddress: this.transferForm.recipientAddress,
        amount: this.transferForm.amount,
        coinType: this.selectedWallet.coinType,
        gasOption: this.transferForm.gasOption,
        customGasFee: this.showCustomGas ? this.transferForm.customGasFee : null,
        memo: this.transferForm.memo,
        isScheduled: this.transferForm.isScheduled,
        scheduleDate: this.transferForm.scheduleDate,
        scheduleTime: this.transferForm.scheduleTime
      }
      
      this.$api.post('/wallet-transactions/transfer', transferData).then(res => {
        uni.showToast({
          title: this.transferForm.isScheduled ? '转账已安排' : '转账提交成功',
          icon: 'success'
        })
        
        this.closeConfirmPopup()
        
        // 跳转到交易详情
        setTimeout(() => {
          uni.navigateTo({
            url: `/pages/wallet/transaction-detail?txId=${res.data.id}`
          })
        }, 1500)
        
      }).catch(err => {
        uni.showToast({
          title: '转账失败',
          icon: 'error'
        })
      }).finally(() => {
        this.isProcessing = false
      })
    },
    
    // 打开扫码
    openScan() {
      // this.$refs.scanPopup.open()
      uni.scanCode({
        success: (res) => {
          this.handleScanResult(res.result)
        },
        fail: (err) => {
          console.error('扫码失败:', err)
        }
      })
    },
    
    // 关闭扫码
    closeScanPopup() {
      this.$refs.scanPopup.close()
    },
    
    // 从相册选择
    chooseFromAlbum() {
      uni.chooseImage({
        count: 1,
        sourceType: ['album'],
        success: (res) => {
          // 处理图片中的二维码
          this.handleImageQRCode(res.tempFilePaths[0])
        }
      })
    },
    
    // 处理扫码结果
    handleScanResult(result) {
      try {
        // 解析二维码内容
        if (result.startsWith('ethereum:') || result.startsWith('0x')) {
          // 以太坊地址格式
          const address = result.replace('ethereum:', '').split('?')[0]
          this.transferForm.recipientAddress = address
          
          // 解析额外参数
          const url = new URL(result.replace('ethereum:', 'http://example.com/'))
          if (url.searchParams.get('value')) {
            this.transferForm.amount = url.searchParams.get('value')
          }
          
        } else if (result.includes('address=')) {
          // MEH格式
          const params = new URLSearchParams(result.split('?')[1])
          this.transferForm.recipientAddress = params.get('address')
          if (params.get('amount')) {
            this.transferForm.amount = params.get('amount')
          }
          
        } else {
          // 直接地址
          this.transferForm.recipientAddress = result
        }
        
        this.closeScanPopup()
        
      } catch (err) {
        uni.showToast({
          title: '二维码格式错误',
          icon: 'error'
        })
      }
    },
    
    // 显示矿工费说明
    showGasInfo() {
      uni.showModal({
        title: '网络费用说明',
        content: '网络费用是支付给区块链网络矿工的费用，用于处理和确认您的交易。费用越高，交易确认越快。',
        showCancel: false
      })
    },
    
    // 获取币种图标
    getCoinIcon(coinType) {
      return `/static/images/coins/${coinType.toLowerCase()}.png`
    },
    
    // 格式化地址
    formatAddress(address) {
      if (!address) return ''
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss">
.transfer-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  padding-top: var(--status-bar-height);
  background: #fff;
  border-bottom: 1rpx solid #e0e0e0;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.wallet-section,
.recipient-section,
.amount-section,
.gas-section,
.advanced-section,
.summary-section {
  margin: 24rpx 32rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.available-balance {
  font-size: 24rpx;
  color: #667eea;
}

.wallet-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  
  &.active {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
  }
}

.wallet-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.wallet-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coin-icon {
  width: 40rpx;
  height: 40rpx;
}

.wallet-details {
  flex: 1;
}

.wallet-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.wallet-balance {
  font-size: 24rpx;
  color: #666;
}

.recipient-input-container {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  margin-bottom: 16rpx;
  
  &.focus {
    border-color: #667eea;
  }
}

.recipient-input {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  border: none;
  background: transparent;
}

.input-actions {
  display: flex;
  gap: 16rpx;
}

.recent-contacts {
  margin-top: 24rpx;
}

.contacts-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.contacts-scroll {
  white-space: nowrap;
}

.contact-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  margin-right: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  min-width: 120rpx;
}

.contact-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
  overflow: hidden;
}

.contact-name {
  font-size: 22rpx;
  color: #333;
  text-align: center;
}

.address-validation {
  margin-top: 16rpx;
}

.validation-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx;
  border-radius: 8rpx;
  
  &.valid {
    background: rgba(76, 205, 196, 0.1);
    color: #4ECDC4;
  }
  
  &.invalid {
    background: rgba(255, 107, 107, 0.1);
    color: #FF6B6B;
  }
}

.validation-text {
  font-size: 24rpx;
}

.amount-input-container {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.amount-input {
  flex: 1;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  text-align: right;
  border: none;
  background: transparent;
}

.currency-display {
  padding: 16rpx 24rpx;
  background: #fff;
  border-radius: 8rpx;
  margin-left: 16rpx;
}

.currency-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #667eea;
}

.amount-converter {
  text-align: center;
  margin-bottom: 24rpx;
}

.convert-text {
  font-size: 24rpx;
  color: #666;
}

.quick-amounts {
  display: flex;
  gap: 12rpx;
}

.quick-amount-btn {
  flex: 1;
  padding: 20rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 8rpx;
  text-align: center;
  
  &:active {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
  }
}

.amount-text {
  font-size: 24rpx;
  color: #333;
}

.gas-options {
  margin-bottom: 24rpx;
}

.gas-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  margin-bottom: 12rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  
  &.active {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
  }
}

.gas-info {
  flex: 1;
}

.gas-type {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.gas-time {
  font-size: 22rpx;
  color: #666;
}

.gas-fee {
  text-align: right;
}

.fee-amount {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}

.fee-fiat {
  font-size: 22rpx;
  color: #666;
}

.custom-gas-input {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.gas-input {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  border: none;
  background: transparent;
}

.gas-unit {
  font-size: 24rpx;
  color: #666;
}

.gas-toggle {
  text-align: center;
}

.toggle-text {
  font-size: 24rpx;
  color: #667eea;
}

.section-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.toggle-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.advanced-content {
  margin-top: 24rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.memo-input {
  position: relative;
  margin-bottom: 32rpx;
}

.input-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.memo-field {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  background: #fff;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #333;
  border: none;
  resize: none;
}

.memo-count {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  font-size: 22rpx;
  color: #999;
}

.schedule-option,
.batch-option {
  margin-bottom: 32rpx;
}

.option-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.option-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.schedule-content {
  padding: 24rpx;
  background: #fff;
  border-radius: 8rpx;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.picker-label {
  font-size: 26rpx;
  color: #333;
}

.picker-value {
  font-size: 26rpx;
  color: #667eea;
}

.batch-content {
  padding: 24rpx;
  background: #fff;
  border-radius: 8rpx;
}

.batch-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 24rpx;
}

.add-recipient-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 20rpx;
  background: rgba(102, 126, 234, 0.1);
  border: 2rpx dashed #667eea;
  border-radius: 8rpx;
  color: #667eea;
}

.summary-content {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
  
  &.total {
    font-weight: bold;
    font-size: 28rpx;
  }
}

.item-label {
  font-size: 26rpx;
  color: #666;
}

.item-value {
  font-size: 26rpx;
  color: #333;
}

.summary-divider {
  height: 2rpx;
  background: #e0e0e0;
  margin: 24rpx 0;
}

.summary-fiat {
  text-align: center;
  margin-top: 16rpx;
}

.fiat-text {
  font-size: 24rpx;
  color: #666;
}

.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background: #fff;
  border-top: 1rpx solid #e0e0e0;
}

.transfer-btn {
  width: 100%;
  height: 88rpx;
  background: #667eea;
  border: none;
  border-radius: 16rpx;
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  
  &.disabled {
    background: #ccc;
    color: #999;
  }
}

.wallet-popup,
.address-book-popup {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.wallet-list,
.address-list {
  padding: 0 32rpx 32rpx 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.wallet-item,
.address-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-container {
  padding: 0 32rpx 24rpx 32rpx;
}

.search-input {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.search-input input {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  border: none;
  background: transparent;
}

.contact-info {
  flex: 1;
  margin-left: 16rpx;
}

.contact-address {
  font-size: 22rpx;
  color: #666;
  margin-top: 4rpx;
}

.contact-type {
  text-align: right;
}

.type-tag {
  font-size: 22rpx;
  color: #667eea;
  padding: 4rpx 12rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12rpx;
}

.confirm-popup {
  width: 600rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 24rpx;
}

.confirm-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 32rpx;
}

.confirm-content {
  margin-bottom: 32rpx;
}

.confirm-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  
  &.total {
    font-weight: bold;
    border-bottom: none;
    margin-top: 16rpx;
    padding-top: 16rpx;
    border-top: 2rpx solid #e0e0e0;
  }
}

.confirm-label {
  font-size: 26rpx;
  color: #666;
}

.confirm-wallet {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.confirm-address,
.confirm-amount,
.confirm-fee,
.confirm-total,
.confirm-memo {
  font-size: 26rpx;
  color: #333;
}

.security-section {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.security-title {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.security-options {
  display: flex;
  gap: 16rpx;
}

.security-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 8rpx;
}

.security-text {
  font-size: 22rpx;
  color: #333;
}

.confirm-actions {
  display: flex;
  gap: 16rpx;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #667eea;
  color: #fff;
}

.password-popup {
  width: 500rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 24rpx;
}

.password-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 32rpx;
}

.password-input {
  margin-bottom: 32rpx;
}

.password-input input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  font-size: 32rpx;
  text-align: center;
  letter-spacing: 8rpx;
}

.password-actions {
  display: flex;
  gap: 16rpx;
}

.scan-popup {
  width: 600rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 24rpx;
}

.scan-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16rpx;
}

.scan-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-bottom: 32rpx;
}

.scan-frame {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
}

.scan-area {
  position: relative;
  width: 400rpx;
  height: 400rpx;
  border: 4rpx solid #667eea;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-tip {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  padding: 0 32rpx;
}

.scan-actions {
  display: flex;
  gap: 16rpx;
}

.scan-cancel-btn,
.scan-album-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 26rpx;
}

.scan-cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.scan-album-btn {
  background: #667eea;
  color: #fff;
}

.empty-contacts {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
