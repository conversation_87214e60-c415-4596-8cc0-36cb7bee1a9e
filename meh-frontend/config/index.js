/**
 * 全局配置
 */
export default {
  // API基础URL
  baseUrl: 'http://localhost:8080/api',

  // 上传文件URL
  uploadUrl: 'http://localhost:8080/api/file/upload',

  // 图片基础URL
  imageUrl: 'http://localhost:8080/api/file/',
  
  // 默认头像
  defaultAvatar: '/static/images/default-avatar.png',
  
  // 默认名片背景
  defaultCardBg: '/static/images/default-card-bg.png',
  
  // 微信小程序appId
  appId: 'wx123456789',
  
  // 版本号
  version: '1.0.0',
  
  // Polygon网络配置
  polygon: {
    // 主网配置
    mainnet: {
      chainId: 137,
      name: 'Polygon Mainnet',
      currency: 'MATIC',
      rpcUrl: 'https://polygon-rpc.com',
      blockExplorer: 'https://polygonscan.com'
    },
    // 测试网配置
    testnet: {
      chainId: 80001,
      name: 'Polygon Mumbai Testnet',
      currency: 'MATIC',
      rpcUrl: 'https://rpc-mumbai.maticvigil.com',
      blockExplorer: 'https://mumbai.polygonscan.com'
    },
    // 当前使用的网络
    currentNetwork: 'testnet'
  },

  // MEH Token配置
  mehToken: {
    name: 'MEH Business Card Token',
    symbol: 'MBC',
    decimals: 18,
    contractAddress: '', // 部署后填入
    icon: '/static/images/mbc-token.png'
  },

  // 积分规则
  pointsRules: {
    // 每日登录
    dailyLogin: 5,
    // 连续登录额外奖励
    continuousLogin3: 2,
    continuousLogin7: 5,
    continuousLogin15: 10,
    continuousLogin30: 15,
    // 完善信息
    completeInfo: 20,
    createCard: 50,
    // 社交互动
    like: 2,
    comment: 5,
    share: 10,
    beLiked: 2,
    beCommented: 3,
    // 人脉拓展
    addContact: 5,
    inviteFriend: 100
  }
}
