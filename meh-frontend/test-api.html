<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MEH名片系统 - API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #3498db;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #e74c3c;
            background-color: #fdf2f2;
        }
        .success {
            border-left-color: #27ae60;
            background-color: #f2fdf5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🃏 MEH名片系统 - API测试页面</h1>
        
        <div class="test-section">
            <h3>🔗 后端连接测试</h3>
            <button onclick="testHealthCheck()">健康检查</button>
            <button onclick="testSwaggerDocs()">Swagger文档</button>
            <div id="connection-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>👤 用户认证测试</h3>
            <button onclick="testUserLogin()">模拟登录</button>
            <button onclick="testUserRegister()">模拟注册</button>
            <div id="auth-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🃏 名片API测试</h3>
            <button onclick="testCardList()">获取名片列表</button>
            <button onclick="testCardCreate()">创建名片</button>
            <div id="card-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📞 联系人API测试</h3>
            <button onclick="testContactList()">获取联系人列表</button>
            <button onclick="testContactCreate()">创建联系人</button>
            <div id="contact-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';

        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result ' + (isError ? 'error' : 'success');
            element.textContent = content;
        }

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.text();
                return {
                    ok: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    ok: false,
                    error: error.message
                };
            }
        }

        async function testHealthCheck() {
            const result = await makeRequest(`${API_BASE}/actuator/health`);
            if (result.ok) {
                showResult('connection-result', `✅ 后端健康检查成功!\nStatus: ${result.status}\nResponse: ${result.data}`);
            } else {
                showResult('connection-result', `❌ 后端健康检查失败!\nError: ${result.error || result.status}`, true);
            }
        }

        async function testSwaggerDocs() {
            const result = await makeRequest(`${API_BASE}/v3/api-docs`);
            if (result.ok) {
                showResult('connection-result', `✅ Swagger文档获取成功!\nStatus: ${result.status}\nAPI文档大小: ${result.data.length} 字符`);
            } else {
                showResult('connection-result', `❌ Swagger文档获取失败!\nError: ${result.error || result.status}`, true);
            }
        }

        async function testUserLogin() {
            const loginData = {
                username: "<EMAIL>",
                password: "123456"
            };
            
            const result = await makeRequest(`${API_BASE}/auth/login`, {
                method: 'POST',
                body: JSON.stringify(loginData)
            });
            
            if (result.ok) {
                showResult('auth-result', `✅ 登录API测试成功!\nStatus: ${result.status}\nResponse: ${result.data}`);
            } else {
                showResult('auth-result', `ℹ️ 登录API响应 (预期可能失败):\nStatus: ${result.status}\nResponse: ${result.data || result.error}`, false);
            }
        }

        async function testUserRegister() {
            const registerData = {
                username: "testuser",
                email: "<EMAIL>", 
                password: "123456",
                confirmPassword: "123456"
            };
            
            const result = await makeRequest(`${API_BASE}/auth/register`, {
                method: 'POST',
                body: JSON.stringify(registerData)
            });
            
            if (result.ok) {
                showResult('auth-result', `✅ 注册API测试成功!\nStatus: ${result.status}\nResponse: ${result.data}`);
            } else {
                showResult('auth-result', `ℹ️ 注册API响应 (预期可能失败):\nStatus: ${result.status}\nResponse: ${result.data || result.error}`, false);
            }
        }

        async function testCardList() {
            const result = await makeRequest(`${API_BASE}/cards`);
            
            if (result.ok) {
                showResult('card-result', `✅ 名片列表API测试成功!\nStatus: ${result.status}\nResponse: ${result.data}`);
            } else {
                showResult('card-result', `ℹ️ 名片列表API响应:\nStatus: ${result.status}\nResponse: ${result.data || result.error}`, result.status === 401);
            }
        }

        async function testCardCreate() {
            const cardData = {
                name: "测试名片",
                title: "软件工程师",
                company: "科技公司",
                phone: "13800138000",
                email: "<EMAIL>"
            };
            
            const result = await makeRequest(`${API_BASE}/cards`, {
                method: 'POST',
                body: JSON.stringify(cardData)
            });
            
            if (result.ok) {
                showResult('card-result', `✅ 创建名片API测试成功!\nStatus: ${result.status}\nResponse: ${result.data}`);
            } else {
                showResult('card-result', `ℹ️ 创建名片API响应:\nStatus: ${result.status}\nResponse: ${result.data || result.error}`, result.status === 401);
            }
        }

        async function testContactList() {
            const result = await makeRequest(`${API_BASE}/contacts`);
            
            if (result.ok) {
                showResult('contact-result', `✅ 联系人列表API测试成功!\nStatus: ${result.status}\nResponse: ${result.data}`);
            } else {
                showResult('contact-result', `ℹ️ 联系人列表API响应:\nStatus: ${result.status}\nResponse: ${result.data || result.error}`, result.status === 401);
            }
        }

        async function testContactCreate() {
            const contactData = {
                name: "测试联系人",
                phone: "13900139000",
                email: "<EMAIL>",
                company: "合作公司"
            };
            
            const result = await makeRequest(`${API_BASE}/contacts`, {
                method: 'POST',
                body: JSON.stringify(contactData)
            });
            
            if (result.ok) {
                showResult('contact-result', `✅ 创建联系人API测试成功!\nStatus: ${result.status}\nResponse: ${result.data}`);
            } else {
                showResult('contact-result', `ℹ️ 创建联系人API响应:\nStatus: ${result.status}\nResponse: ${result.data || result.error}`, result.status === 401);
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            testHealthCheck();
        }
    </script>
</body>
</html>
