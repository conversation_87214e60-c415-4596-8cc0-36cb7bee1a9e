/**
 * Polygon区块链API接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */

import request from '@/utils/request.js'

/**
 * Polygon区块链API
 */
export default {
  
  /**
   * 生成Polygon钱包地址
   * @param {string} mnemonic 助记词
   * @param {string} password 密码（可选）
   */
  generateAddress(mnemonic, password = '') {
    return request.post('/polygon/generate-address', {
      mnemonic,
      password
    })
  },
  
  /**
   * 获取MATIC余额
   * @param {string} address 钱包地址
   */
  getMaticBalance(address) {
    return request.get(`/polygon/balance/matic/${address}`)
  },
  
  /**
   * 获取Token余额
   * @param {string} address 钱包地址
   * @param {string} contractAddress Token合约地址
   */
  getTokenBalance(address, contractAddress) {
    return request.get(`/polygon/balance/token/${address}`, {
      contractAddress
    })
  },
  
  /**
   * 发送MATIC转账
   * @param {object} params 转账参数
   */
  sendMatic(params) {
    return request.post('/polygon/transfer/matic', params)
  },
  
  /**
   * 发送Token转账
   * @param {object} params 转账参数
   */
  sendToken(params) {
    return request.post('/polygon/transfer/token', params)
  },
  
  /**
   * 估算Gas费用
   * @param {object} params 估算参数
   */
  estimateGasFee(params) {
    return request.post('/polygon/estimate-gas', params)
  },
  
  /**
   * 获取交易详情
   * @param {string} txHash 交易哈希
   */
  getTransactionDetails(txHash) {
    return request.get(`/polygon/transaction/${txHash}`)
  },
  
  /**
   * 获取交易历史
   * @param {string} address 钱包地址
   * @param {number} page 页码
   * @param {number} size 每页大小
   */
  getTransactionHistory(address, page = 1, size = 20) {
    return request.get(`/polygon/transactions/${address}`, {
      page,
      size
    })
  },
  
  /**
   * 验证地址格式
   * @param {string} address 钱包地址
   */
  validateAddress(address) {
    return request.get(`/polygon/validate-address/${address}`)
  },
  
  /**
   * 获取当前Gas价格
   */
  getCurrentGasPrice() {
    return request.get('/polygon/gas-price')
  },
  
  /**
   * 获取网络状态
   */
  getNetworkStatus() {
    return request.get('/polygon/network-status')
  },
  
  /**
   * 部署MEH Token合约
   * @param {string} deployerPrivateKey 部署者私钥
   */
  deployMehToken(deployerPrivateKey) {
    return request.post('/polygon/deploy-meh-token', {
      deployerPrivateKey
    })
  },
  
  /**
   * 铸造MEH Token
   * @param {object} params 铸造参数
   */
  mintMehToken(params) {
    return request.post('/polygon/mint-meh-token', params)
  },
  
  /**
   * 获取MEH Token信息
   */
  getMehTokenInfo() {
    return request.get('/polygon/meh-token-info')
  },
  
  /**
   * 获取多个地址的余额（批量查询）
   * @param {array} addresses 地址数组
   * @param {string} coinType 币种类型
   */
  async getBatchBalances(addresses, coinType = 'MATIC') {
    const promises = addresses.map(address => {
      if (coinType === 'MATIC') {
        return this.getMaticBalance(address)
      } else {
        // 需要合约地址
        return this.getTokenBalance(address, this.getContractAddress(coinType))
      }
    })
    
    try {
      const results = await Promise.all(promises)
      return results.map((result, index) => ({
        address: addresses[index],
        balance: result.data || 0,
        coinType
      }))
    } catch (error) {
      console.error('批量查询余额失败:', error)
      return addresses.map(address => ({
        address,
        balance: 0,
        coinType,
        error: true
      }))
    }
  },
  
  /**
   * 获取合约地址
   * @param {string} coinType 币种类型
   */
  getContractAddress(coinType) {
    const contracts = {
      'MBC': '', // MEH Token合约地址，部署后填入
      'USDT': '******************************************', // Polygon USDT
      'USDC': '******************************************', // Polygon USDC
      'ETH': '******************************************'   // Polygon ETH
    }
    
    return contracts[coinType] || ''
  },
  
  /**
   * 检查交易状态
   * @param {string} txHash 交易哈希
   * @param {number} maxRetries 最大重试次数
   * @param {number} interval 重试间隔（毫秒）
   */
  async checkTransactionStatus(txHash, maxRetries = 30, interval = 2000) {
    for (let i = 0; i < maxRetries; i++) {
      try {
        const result = await this.getTransactionDetails(txHash)
        
        if (result.data && result.data.status) {
          return {
            success: true,
            status: result.data.status,
            data: result.data
          }
        }
        
        // 等待下次检查
        await new Promise(resolve => setTimeout(resolve, interval))
        
      } catch (error) {
        console.error(`检查交易状态失败 (第${i + 1}次):`, error)
        
        if (i === maxRetries - 1) {
          return {
            success: false,
            error: '交易状态检查超时'
          }
        }
        
        await new Promise(resolve => setTimeout(resolve, interval))
      }
    }
    
    return {
      success: false,
      error: '交易状态检查超时'
    }
  },
  
  /**
   * 获取推荐Gas价格
   */
  async getRecommendedGasPrice() {
    try {
      const result = await this.getCurrentGasPrice()
      const currentGasPrice = result.data
      
      // 返回不同速度的Gas价格建议
      return {
        slow: Math.floor(currentGasPrice * 0.8),
        standard: currentGasPrice,
        fast: Math.floor(currentGasPrice * 1.2),
        instant: Math.floor(currentGasPrice * 1.5)
      }
    } catch (error) {
      console.error('获取推荐Gas价格失败:', error)
      
      // 返回默认值
      return {
        slow: 25,
        standard: 30,
        fast: 35,
        instant: 40
      }
    }
  },
  
  /**
   * 格式化交易参数
   * @param {object} params 原始参数
   */
  formatTransactionParams(params) {
    return {
      fromAddress: params.fromAddress,
      toAddress: params.toAddress,
      amount: params.amount.toString(),
      gasPrice: params.gasPrice ? params.gasPrice.toString() : undefined,
      gasLimit: params.gasLimit ? params.gasLimit.toString() : undefined,
      privateKey: params.privateKey,
      contractAddress: params.contractAddress
    }
  }
}
