{"name": "meh-business-card", "version": "1.0.0", "description": "MEH电子名片微信小程序", "main": "main.js", "dependencies": {"uview-ui": "^2.0.31"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "^3.0.0-alpha-3040020230427001", "@dcloudio/uni-cli-shared": "^3.0.0-alpha-3040020230427001", "@dcloudio/uni-stacktracey": "^3.0.0-alpha-3040020230427001", "@dcloudio/vue-cli-plugin-uni": "^3.0.0-alpha-3040020230427001", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-import": "^1.13.3", "cross-env": "^7.0.2", "jest": "^25.4.0", "mini-types": "*", "miniprogram-api-typings": "*", "postcss-comment": "^2.0.0", "vue": "^2.6.11", "vue-template-compiler": "^2.6.11"}, "scripts": {"serve": "npm run dev:h5", "build": "npm run build:mp-weixin", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT"}