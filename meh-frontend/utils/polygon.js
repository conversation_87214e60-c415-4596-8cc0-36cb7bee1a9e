/**
 * Polygon区块链工具类
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */

import config from '@/config/index.js'

/**
 * Polygon网络工具类
 */
class PolygonUtils {
  
  /**
   * 获取当前网络配置
   */
  static getCurrentNetwork() {
    const currentNetwork = config.polygon.currentNetwork
    return config.polygon[currentNetwork]
  }
  
  /**
   * 获取网络名称
   */
  static getNetworkName() {
    return this.getCurrentNetwork().name
  }
  
  /**
   * 获取链ID
   */
  static getChainId() {
    return this.getCurrentNetwork().chainId
  }
  
  /**
   * 获取区块浏览器URL
   */
  static getBlockExplorerUrl() {
    return this.getCurrentNetwork().blockExplorer
  }
  
  /**
   * 生成交易链接
   * @param {string} txHash 交易哈希
   */
  static getTxUrl(txHash) {
    return `${this.getBlockExplorerUrl()}/tx/${txHash}`
  }
  
  /**
   * 生成地址链接
   * @param {string} address 钱包地址
   */
  static getAddressUrl(address) {
    return `${this.getBlockExplorerUrl()}/address/${address}`
  }
  
  /**
   * 验证地址格式
   * @param {string} address 钱包地址
   */
  static isValidAddress(address) {
    if (!address) return false
    return /^0x[a-fA-F0-9]{40}$/.test(address)
  }
  
  /**
   * 格式化地址显示
   * @param {string} address 钱包地址
   * @param {number} startLength 开头显示长度
   * @param {number} endLength 结尾显示长度
   */
  static formatAddress(address, startLength = 6, endLength = 4) {
    if (!address) return ''
    if (address.length <= startLength + endLength) return address
    
    return `${address.substring(0, startLength)}...${address.substring(address.length - endLength)}`
  }
  
  /**
   * 格式化Token数量
   * @param {string|number} amount Token数量
   * @param {number} decimals Token精度
   * @param {number} displayDecimals 显示精度
   */
  static formatTokenAmount(amount, decimals = 18, displayDecimals = 4) {
    if (!amount) return '0'
    
    const num = parseFloat(amount)
    if (num === 0) return '0'
    
    // 如果数量很小，显示更多小数位
    if (num < 0.0001) {
      return num.toExponential(2)
    }
    
    return num.toFixed(displayDecimals).replace(/\.?0+$/, '')
  }
  
  /**
   * 格式化MATIC数量
   * @param {string|number} amount MATIC数量
   */
  static formatMaticAmount(amount) {
    return this.formatTokenAmount(amount, 18, 6)
  }
  
  /**
   * 格式化Gas费用
   * @param {string|number} gasFee Gas费用（Wei）
   */
  static formatGasFee(gasFee) {
    if (!gasFee) return '0'
    
    const maticAmount = parseFloat(gasFee) / Math.pow(10, 18)
    return this.formatMaticAmount(maticAmount)
  }
  
  /**
   * 获取币种图标
   * @param {string} coinType 币种类型
   */
  static getCoinIcon(coinType) {
    const icons = {
      'MATIC': '/static/images/coins/matic.png',
      'MBC': '/static/images/coins/mbc.png',
      'USDT': '/static/images/coins/usdt.png',
      'USDC': '/static/images/coins/usdc.png',
      'ETH': '/static/images/coins/eth.png'
    }
    
    return icons[coinType] || '/static/images/coins/default.png'
  }
  
  /**
   * 获取币种名称
   * @param {string} coinType 币种类型
   */
  static getCoinName(coinType) {
    const names = {
      'MATIC': 'Polygon',
      'MBC': 'MEH Business Card Token',
      'USDT': 'Tether USD',
      'USDC': 'USD Coin',
      'ETH': 'Ethereum'
    }
    
    return names[coinType] || coinType
  }
  
  /**
   * 检查是否为原生币
   * @param {string} coinType 币种类型
   */
  static isNativeCoin(coinType) {
    return coinType === 'MATIC'
  }
  
  /**
   * 检查是否为MEH Token
   * @param {string} coinType 币种类型
   */
  static isMehToken(coinType) {
    return coinType === 'MBC'
  }
  
  /**
   * 获取交易状态文本
   * @param {string} status 交易状态
   */
  static getTransactionStatusText(status) {
    const statusMap = {
      'pending': '待确认',
      'confirmed': '已确认',
      'failed': '失败',
      'success': '成功'
    }
    
    return statusMap[status] || '未知'
  }
  
  /**
   * 获取交易类型文本
   * @param {string} type 交易类型
   */
  static getTransactionTypeText(type) {
    const typeMap = {
      'send': '发送',
      'receive': '接收',
      'mint': '铸造',
      'burn': '销毁',
      'approve': '授权',
      'contract': '合约调用'
    }
    
    return typeMap[type] || '未知'
  }
  
  /**
   * 计算交易费用（美元）
   * @param {string|number} gasFee Gas费用（MATIC）
   * @param {number} maticPrice MATIC价格（美元）
   */
  static calculateTransactionFeeUSD(gasFee, maticPrice) {
    if (!gasFee || !maticPrice) return 0
    
    const maticAmount = parseFloat(gasFee)
    return (maticAmount * maticPrice).toFixed(2)
  }
  
  /**
   * 生成二维码数据
   * @param {string} address 钱包地址
   * @param {string} amount 金额（可选）
   */
  static generateQRData(address, amount = null) {
    let qrData = `ethereum:${address}`
    
    if (amount) {
      qrData += `?value=${amount}`
    }
    
    return qrData
  }
  
  /**
   * 复制到剪贴板
   * @param {string} text 要复制的文本
   */
  static copyToClipboard(text) {
    return new Promise((resolve, reject) => {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: '已复制',
            icon: 'success'
          })
          resolve()
        },
        fail: (error) => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          })
          reject(error)
        }
      })
    })
  }
  
  /**
   * 打开区块浏览器
   * @param {string} url 浏览器URL
   */
  static openBlockExplorer(url) {
    // #ifdef H5
    window.open(url, '_blank')
    // #endif
    
    // #ifdef MP-WEIXIN
    uni.setClipboardData({
      data: url,
      success: () => {
        uni.showModal({
          title: '提示',
          content: '链接已复制到剪贴板，请在浏览器中打开',
          showCancel: false
        })
      }
    })
    // #endif
  }
}

export default PolygonUtils
